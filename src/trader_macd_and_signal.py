# coding=utf-8
import numpy as np
import pandas as pd
from xtquant import xtdata
from xtquant import xttrader
from xtquant import xtconstant
from xtquant.xttype import StockAccount
import talib


class MACDHistogramStrategy:
    """
    基于MACD直方图一阶差分的交易策略
    
    策略逻辑：
    1. 计算日线和60分钟的MACD直方图
    2. 计算直方图的一阶差分
    3. 当日线和60分钟直方图差分同时向上时买入
    4. 当日线和60分钟直方图差分同时向下时卖出
    """
    
    def __init__(self, stock_code='000001.SZ', initial_cash=1000000):
        self.stock_code = stock_code
        self.initial_cash = initial_cash
        self.position = 0  # 持仓数量
        self.cash = initial_cash  # 可用资金
        
        # MACD参数
        self.fast_period = 12
        self.slow_period = 26
        self.signal_period = 9
        
        # 交易参数
        self.min_data_length = 50  # 最少需要的数据长度
        self.trade_volume_ratio = 0.8  # 每次交易使用的资金比例
        
        # 初始化交易客户端（模拟交易）
        self.trader = None
        self.account = None
        
    def init_trader(self, path, session_id):
        """初始化交易客户端"""
        try:
            self.trader = xttrader.XtQuantTrader(path, session_id)
            connect_result = self.trader.connect()
            if connect_result == 0:
                print("交易客户端连接成功")
                # 使用模拟账号
                self.account = StockAccount('simulation_account')
                self.trader.subscribe(self.account)
                return True
            else:
                print(f"交易客户端连接失败: {connect_result}")
                return False
        except Exception as e:
            print(f"初始化交易客户端失败: {e}")
            return False
    
    def calculate_macd_histogram(self, prices, fast=12, slow=26, signal=9):
        """
        计算MACD直方图
        
        Args:
            prices: 价格序列
            fast: 快线周期
            slow: 慢线周期  
            signal: 信号线周期
            
        Returns:
            histogram: MACD直方图
        """
        try:
            # 计算MACD
            macd_line, signal_line, histogram = talib.MACD(
                prices, 
                fastperiod=fast, 
                slowperiod=slow, 
                signalperiod=signal
            )
            return histogram
        except Exception as e:
            print(f"计算MACD失败: {e}")
            return None
    
    def calculate_first_difference(self, data):
        """
        计算一阶差分
        
        Args:
            data: 数据序列
            
        Returns:
            diff: 一阶差分序列
        """
        if len(data) < 2:
            return np.array([0])
        
        return np.diff(data)
    
    def get_market_data(self, period='1d', count=100):
        """
        获取市场数据
        
        Args:
            period: 周期 ('1d', '60m')
            count: 数据条数
            
        Returns:
            DataFrame: 市场数据
        """
        try:
            # 获取历史数据
            xtdata.download_history_data2([self.stock_code], period=period)  # 下载历史数据

            # 获取前需要先订阅，该数据通过指定period = 'limitupperformance'获取
            xtdata.subscribe_quote(self.stock_code, period=period, count=-1)

            data = xtdata.get_market_data_ex([],
                                             [self.stock_code],
                                             period=period,
                                             start_time="",
                                             end_time="",
                                             count=100)
            if self.stock_code in data:
                df = data[self.stock_code]
                return df
            else:
                print(f"未获取到{self.stock_code}的数据")
                return None
                
        except Exception as e:
            print(f"获取市场数据失败: {e}")
            return None
    
    def generate_signals(self):
        """
        生成交易信号
        
        Returns:
            signal: 1=买入, -1=卖出, 0=无操作
        """
        try:
            # 获取日线数据
            daily_data = self.get_market_data(period='1d', count=50)
            if daily_data is None or len(daily_data) < self.min_data_length:
                print("日线数据不足")
                return 0
            
            # 获取60分钟数据
            hourly_data = self.get_market_data(period='60m', count=50)
            if hourly_data is None or len(hourly_data) < self.min_data_length:
                print("60分钟数据不足")
                return 0
            
            # 计算日线MACD直方图
            daily_close = daily_data['close'].values
            daily_histogram = self.calculate_macd_histogram(
                daily_close, self.fast_period, self.slow_period, self.signal_period
            )
            
            # 计算60分钟MACD直方图
            hourly_close = hourly_data['close'].values
            hourly_histogram = self.calculate_macd_histogram(
                hourly_close, self.fast_period, self.slow_period, self.signal_period
            )
            
            if daily_histogram is None or hourly_histogram is None:
                print("MACD计算失败")
                return 0
            
            # 移除NaN值
            daily_histogram = daily_histogram[~np.isnan(daily_histogram)]
            hourly_histogram = hourly_histogram[~np.isnan(hourly_histogram)]
            
            if len(daily_histogram) < 3 or len(hourly_histogram) < 3:
                print("有效数据不足")
                return 0
            
            # 计算一阶差分
            daily_diff = self.calculate_first_difference(daily_histogram)
            hourly_diff = self.calculate_first_difference(hourly_histogram)
            
            # 获取最新的差分值
            latest_daily_diff = daily_diff[-1] if len(daily_diff) > 0 else 0
            latest_hourly_diff = hourly_diff[-1] if len(hourly_diff) > 0 else 0
            
            # 获取前一个差分值用于确认趋势
            prev_daily_diff = daily_diff[-2] if len(daily_diff) > 1 else 0
            prev_hourly_diff = hourly_diff[-2] if len(hourly_diff) > 1 else 0
            
            print(f"日线直方图差分: 当前={latest_daily_diff:.6f}, 前一个={prev_daily_diff:.6f}")
            print(f"60分钟直方图差分: 当前={latest_hourly_diff:.6f}, 前一个={prev_hourly_diff:.6f}")
            
            # 生成交易信号
            # 买入信号：两个周期的直方图差分都向上且连续两次为正
            if (latest_daily_diff > 0 and latest_hourly_diff > 0 and 
                prev_daily_diff > 0 and prev_hourly_diff > 0):
                print("生成买入信号")
                return 1
            
            # 卖出信号：两个周期的直方图差分都向下且连续两次为负
            elif (latest_daily_diff < 0 and latest_hourly_diff < 0 and 
                  prev_daily_diff < 0 and prev_hourly_diff < 0):
                print("生成卖出信号")
                return -1
            
            else:
                return 0
                
        except Exception as e:
            print(f"生成信号失败: {e}")
            return 0
    
    def execute_trade(self, signal):
        """
        执行交易
        
        Args:
            signal: 交易信号 (1=买入, -1=卖出, 0=无操作)
        """
        if signal == 0:
            return
        
        try:
            # 获取当前价格
            current_data = self.get_market_data(period='1m', count=1)
            if current_data is None or len(current_data) == 0:
                print("无法获取当前价格")
                return
            
            current_price = current_data['close'].iloc[-1]
            
            if signal == 1:  # 买入
                if self.position <= 0:  # 没有持仓或持有空仓
                    # 计算可买入数量
                    available_cash = self.cash * self.trade_volume_ratio
                    volume = int(available_cash / current_price / 100) * 100  # 按手数买入
                    
                    if volume >= 100:  # 至少一手
                        if self.trader and self.account:
                            # 实际下单
                            order_id = self.trader.order_stock(
                                account=self.account,
                                stock_code=self.stock_code,
                                order_type=xtconstant.STOCK_BUY,
                                order_volume=volume,
                                price_type=xtconstant.FIX_PRICE,
                                price=current_price * 1.01,  # 稍高于当前价格确保成交
                                strategy_name='MACD_Histogram_Strategy',
                                order_remark='买入信号'
                            )
                            print(f"提交买入订单: {volume}股, 价格: {current_price:.2f}, 订单ID: {order_id}")
                        else:
                            # 模拟交易
                            self.position += volume
                            self.cash -= volume * current_price
                            print(f"模拟买入: {volume}股, 价格: {current_price:.2f}")
                            print(f"当前持仓: {self.position}股, 剩余资金: {self.cash:.2f}")
                    else:
                        print("资金不足，无法买入")
                else:
                    print("已有持仓，忽略买入信号")
            
            elif signal == -1:  # 卖出
                if self.position > 0:  # 有持仓
                    volume = self.position
                    
                    if self.trader and self.account:
                        # 实际下单
                        order_id = self.trader.order_stock(
                            account=self.account,
                            stock_code=self.stock_code,
                            order_type=xtconstant.STOCK_SELL,
                            order_volume=volume,
                            price_type=xtconstant.FIX_PRICE,
                            price=current_price * 0.99,  # 稍低于当前价格确保成交
                            strategy_name='MACD_Histogram_Strategy',
                            order_remark='卖出信号'
                        )
                        print(f"提交卖出订单: {volume}股, 价格: {current_price:.2f}, 订单ID: {order_id}")
                    else:
                        # 模拟交易
                        self.cash += volume * current_price
                        self.position = 0
                        print(f"模拟卖出: {volume}股, 价格: {current_price:.2f}")
                        print(f"当前持仓: {self.position}股, 总资金: {self.cash:.2f}")
                else:
                    print("无持仓，忽略卖出信号")
                    
        except Exception as e:
            print(f"执行交易失败: {e}")
    
    def run_strategy(self, trader_path=None, session_id=12345):
        """
        运行策略
        
        Args:
            trader_path: 交易客户端路径
            session_id: 会话ID
        """
        print("启动MACD直方图一阶差分策略...")
        print(f"目标股票: {self.stock_code}")
        print(f"初始资金: {self.initial_cash}")
        
        # 如果提供了交易客户端路径，则初始化实盘交易
        if trader_path:
            if not self.init_trader(trader_path, session_id):
                print("将使用模拟交易模式")
        else:
            print("使用模拟交易模式")
        
        try:
            # 生成交易信号
            signal = self.generate_signals()
            
            # 执行交易
            self.execute_trade(signal)
            
            # 计算当前收益
            if self.position > 0:
                current_data = self.get_market_data(period='1m', count=1)
                if current_data is not None and len(current_data) > 0:
                    current_price = current_data['close'].iloc[-1]
                    total_value = self.cash + self.position * current_price
                    return_rate = (total_value - self.initial_cash) / self.initial_cash * 100
                    print(f"当前总资产: {total_value:.2f}, 收益率: {return_rate:.2f}%")
            
        except Exception as e:
            print(f"策略运行失败: {e}")
    
    def backtest(self, start_date='20231101', end_date='20241101'):
        """
        策略回测
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
        """
        print(f"开始回测: {start_date} - {end_date}")
        
        try:
            # 获取回测期间的数据
            daily_data = xtdata.get_market_data(
                field_list=['time', 'close'],
                stock_list=[self.stock_code],
                period='1d',
                start_time=start_date,
                end_time=end_date
            )
            
            if self.stock_code not in daily_data:
                print("获取回测数据失败")
                return
            
            df = daily_data[self.stock_code]
            print(f"回测数据量: {len(df)}条")
            
            # 这里可以实现详细的回测逻辑
            # 由于篇幅限制，此处仅做示例
            print("回测功能需要根据具体需求进一步实现")
            
        except Exception as e:
            print(f"回测失败: {e}")


# 使用示例
if __name__ == "__main__":
    # 创建策略实例
    strategy = MACDHistogramStrategy(
        stock_code='000001.SZ',  # 平安银行
        initial_cash=1000000     # 100万初始资金
    )
    
    # 运行策略（模拟交易）
    strategy.run_strategy()

    # 如果要使用实盘交易，需要提供交易客户端路径
    # trader_path = r'C:\迅投极速交易客户端\userdata'
    # strategy.run_strategy(trader_path=trader_path, session_id=12345)
    
    # 运行回测
    # strategy.backtest(start_date='20231101', end_date='20241101')