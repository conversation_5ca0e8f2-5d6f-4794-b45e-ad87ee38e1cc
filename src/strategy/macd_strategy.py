import numpy as np


class MACDStrategy:
    def __init__(self, fast_period=12, slow_period=26, signal_period=9):
        self.fast_period = fast_period
        self.slow_period = slow_period
        self.signal_period = signal_period
        self.macd_line = None
        self.signal_line = None
        self.histogram = None
    
    def calculate_macd(self, prices):
        if len(prices) < self.slow_period:
            return None, None, None
        
        # 计算快线和慢线EMA
        fast_ema = self.calculate_ema(prices, self.fast_period)
        slow_ema = self.calculate_ema(prices, self.slow_period)
        
        # 计算MACD线（DIF线）
        macd_line = [fast_ema[i] - slow_ema[i] for i in range(len(prices))]
        
        # 计算信号线（DEA线）
        signal_line = self.calculate_ema(macd_line, self.signal_period)
        
        # 计算直方图
        histogram = [macd_line[i] - signal_line[i] for i in range(len(prices))]
        
        return macd_line, signal_line, histogram
    
    def calculate_ema(self, prices, period):
        if len(prices) == 0:
            return []
        
        ema_values = [prices[0]]
        multiplier = 2.0 / (period + 1)
        
        for i in range(1, len(prices)):
            ema = (prices[i] * multiplier) + (ema_values[-1] * (1 - multiplier))
            ema_values.append(ema)
        
        return ema_values
    
    def calculate_first_difference(self, data):
        if len(data) < 2:
            return np.array([0])
        
        return np.diff(data)    
    
    
    def generate_signals(self, daily_data, hourly_data):
        pass
    
    
    def get_sql_data(self, stock_code, start_date, end_date):
        pass
    
if __name__=='__main__':
    macd_class = MACDStrategy()
    