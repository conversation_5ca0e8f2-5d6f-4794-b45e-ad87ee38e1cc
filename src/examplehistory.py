from xtquant import xtdata
import datetime
import time


def calculate_date_range(days_back):
    """
    计算指定天数前的日期范围
    :param days_back: 向前追溯的天数
    :return: (start_time, end_time) 格式为YYYYMMDD的字符串
    """
    today = datetime.date.today()
    start_date = today - datetime.timedelta(days=days_back)

    start_time = start_date.strftime('%Y%m%d')
    end_time = today.strftime('%Y%m%d')

    return start_time, end_time


def download_progress_callback(data):
    """
    下载进度回调函数
    :param data: 包含下载进度信息的字典
    """
    try:
        finished = data.get('finished', 0)
        total = data.get('total', 1)
        progress = (finished / total) * 100 if total > 0 else 0

        print(f"\r下载进度: {finished}/{total} ({progress:.1f}%)", end='', flush=True)

        if finished >= total:
            print("\n数据下载完成!")
    except Exception as e:
        print(f"\n进度回调错误: {e}")


def download_single_stock_history(stock_code, days_back, period='1d'):
    """
    使用盘后数据下载方式下载单个股票的历史数据
    :param stock_code: 股票代码
    :param days_back: 向前追溯的天数
    :param period: 数据周期，默认日线'1d'
    :return: bool 下载是否成功
    """
    print(f"=== 单个股票盘后数据下载 ===")
    print(f"股票代码: {stock_code}")
    print(f"数据周期: {period}")
    print(f"追溯天数: {days_back}天")

    # 计算时间范围
    start_time, end_time = calculate_date_range(days_back)
    print(f"时间范围: {start_time} 到 {end_time}")

    try:
        # 方法1: 使用download_history_data下载单个股票
        print(f"\\n开始下载 {stock_code} 的{period}数据...")
        result = xtdata.download_history_data(
            stock_code=stock_code,
            period=period,
            start_time=start_time,
            end_time=end_time
        )

        if result:
            print(f"✓ {stock_code} 数据下载成功")
            return True
        else:
            print(f"✗ {stock_code} 数据下载失败")
            return False

    except Exception as e:
        print(f"下载过程中出错: {e}")
        return False


def download_multiple_stocks_history(stock_list, days_back, period='1d'):
    """
    使用盘后数据下载方式批量下载多个股票的历史数据
    :param stock_list: 股票代码列表
    :param days_back: 向前追溯的天数
    :param period: 数据周期，默认日线'1d'
    :return: bool 下载是否成功
    """
    print(f"\\n=== 批量股票盘后数据下载 ===")
    print(f"股票列表: {stock_list}")
    print(f"数据周期: {period}")
    print(f"追溯天数: {days_back}天")

    # 计算时间范围
    start_time, end_time = calculate_date_range(days_back)
    print(f"时间范围: {start_time} 到 {end_time}")

    try:
        # 方法2: 使用download_history_data2批量下载
        print(f"\\n开始批量下载数据...")
        xtdata.download_history_data2(
            stock_list=stock_list,
            period=period,
            start_time=start_time,
            end_time=end_time,

        )

        print(f"✓ 批量数据下载完成")
        return True

    except Exception as e:
        print(f"批量下载过程中出错: {e}")
        return False


def download_using_supply_method(stock_list, days_back, period='1d'):
    """
    使用supply_history_data方法下载数据（等价于download_history_data）
    :param stock_list: 股票代码列表
    :param days_back: 向前追溯的天数
    :param period: 数据周期，默认日线'1d'
    """
    print(f"\\n=== 使用Supply方法下载数据 ===")
    print(f"股票列表: {stock_list}")
    print(f"数据周期: {period}")
    print(f"追溯天数: {days_back}天")

    # 计算时间范围
    start_time, end_time = calculate_date_range(days_back)
    print(f"时间范围: {start_time} 到 {end_time}")

    try:
        # supply_history_data是download_history_data的别名
        for stock_code in stock_list:
            print(f"下载 {stock_code} 数据...")
            result = xtdata.supply_history_data(
                stock_code=stock_code,
                period=period,
                start_time=start_time,
                end_time=end_time
            )
            if result:
                print(f"✓ {stock_code} 下载成功")
            else:
                print(f"✗ {stock_code} 下载失败")

        print("Supply方法下载完成")

    except Exception as e:
        print(f"Supply方法下载出错: {e}")


def verify_downloaded_data(stock_code, days_back, period='1d'):
    """
    验证下载的数据
    :param stock_code: 股票代码
    :param days_back: 向前追溯的天数
    :param period: 数据周期
    """
    print(f"\\n=== 验证下载的数据 ===")

    # 计算时间范围
    start_time, end_time = calculate_date_range(days_back)

    try:
        # 使用get_market_data获取下载的数据
        data = xtdata.get_market_data(
            field_list=["time", "open", "high", "low", "close", "volume", "amount"],
            stock_list=[stock_code],
            period=period,
            start_time=start_time,
            end_time=end_time,
            dividend_type='none',
            fill_data=True
        )

        if data and 'close' in data:
            close_data = data['close']
            if stock_code in close_data.columns:
                stock_data = close_data[stock_code].dropna()
                print(f"✓ 成功获取 {stock_code} 数据")
                print(f"数据条数: {len(stock_data)}")
                print(f"数据时间范围: {stock_data.index[0]} 到 {stock_data.index[-1]}")
                print(f"最新收盘价: {stock_data.iloc[-1]:.3f}")

                # 显示最近5天的数据
                print("\\n最近5个交易日数据:")
                recent_data = stock_data.tail(5)
                for date, price in recent_data.items():
                    print(f"  {str(date)[:10]}: {price:.3f}")

                return True
            else:
                print(f"✗ 在数据中未找到股票 {stock_code}")
                return False
        else:
            print("✗ 未获取到有效数据")
            return False

    except Exception as e:
        print(f"验证数据时出错: {e}")
        return False


def download_recent_days_data(stock_codes, days_back=100, period='1d', download_method='batch'):
    """
    主函数：下载最近指定天数的日线数据
    :param stock_codes: 股票代码或股票代码列表
    :param days_back: 向前追溯的天数，默认100天
    :param period: 数据周期，默认日线'1d'
    :param download_method: 下载方法 'single', 'batch', 'supply'
    """
    print("=" * 60)
    print("盘后数据下载程序")
    print("=" * 60)

    # 确保stock_codes是列表格式
    if isinstance(stock_codes, str):
        stock_list = [stock_codes]
    else:
        stock_list = stock_codes

    print(f"下载参数:")
    print(f"  股票代码: {stock_list}")
    print(f"  追溯天数: {days_back}天")
    print(f"  数据周期: {period}")
    print(f"  下载方法: {download_method}")

    success = False

    # 根据选择的方法下载数据
    if download_method == 'single':
        # 单个股票下载方式
        for stock_code in stock_list:
            success = download_single_stock_history(stock_code, days_back, period)
            if not success:
                break

    elif download_method == 'batch':
        # 批量下载方式
        success = download_multiple_stocks_history(stock_list, days_back, period)

    elif download_method == 'supply':
        # Supply方法下载
        download_using_supply_method(stock_list, days_back, period)
        success = True

    else:
        print(f"不支持的下载方法: {download_method}")
        return

    # 验证下载结果
    if success:
        print("\\n开始验证下载的数据...")
        for stock_code in stock_list[:2]:  # 只验证前两个股票，避免输出过多
            verify_downloaded_data(stock_code, days_back, period)

    print("\\n" + "=" * 60)
    print("数据下载程序执行完成")
    print("=" * 60)


def main():
    """主函数示例"""

    # 示例1: 下载单个股票最近100天的日线数据
    print("示例1: 下载单个股票最近100天日线数据")
    download_recent_days_data(
        stock_codes="000001.SZ",
        days_back=100,
        period='1d',
        download_method='single'
    )

    time.sleep(2)  # 等待一下

    # 示例2: 批量下载多个股票最近50天的日线数据
    print("\\n" + "=" * 80)
    print("示例2: 批量下载多个股票最近50天日线数据")
    download_recent_days_data(
        stock_codes=["000001.SZ", "000002.SZ", "600000.SH"],
        days_back=50,
        period='1d',
        download_method='batch'
    )

    time.sleep(2)  # 等待一下

    # 示例3: 下载最近30天的60分钟数据
    print("\\n" + "=" * 80)
    print("示例3: 下载最近30天60分钟数据")
    download_recent_days_data(
        stock_codes=["000001.SZ"],
        days_back=30,
        period='1h',  # 60分钟数据
        download_method='batch'
    )


if __name__ == "__main__":
    # 可以直接调用main()运行示例
    # main()

    # 或者自定义参数运行
    # 修改这里的参数来下载您需要的数据

    # 下载最近100天的日线数据
    download_recent_days_data(
        stock_codes=["000001.SZ", "600519.SH"],  # 可以修改为您需要的股票代码
        days_back=100,  # 可以修改为您需要的天数
        period='1d',  # 可以修改为'1h', '30m', '5m'等
        download_method='batch'  # 推荐使用'batch'批量下载
    )
import datetime
import time


def calculate_date_range(days_back):
    """
    计算指定天数前的日期范围
    :param days_back: 向前追溯的天数
    :return: (start_time, end_time) 格式为YYYYMMDD的字符串
    """
    today = datetime.date.today()
    start_date = today - datetime.timedelta(days=days_back)

    start_time = start_date.strftime('%Y%m%d')
    end_time = today.strftime('%Y%m%d')

    return start_time, end_time


def download_progress_callback(data):
    """
    下载进度回调函数
    :param data: 包含下载进度信息的字典
    """
    try:
        finished = data.get('finished', 0)
        total = data.get('total', 1)
        progress = (finished / total) * 100 if total > 0 else 0

        print(f"\r下载进度: {finished}/{total} ({progress:.1f}%)", end='', flush=True)

        if finished >= total:
            print("\n数据下载完成!")
    except Exception as e:
        print(f"\n进度回调错误: {e}")


def download_single_stock_history(stock_code, days_back, period='1d'):
    """
    使用盘后数据下载方式下载单个股票的历史数据
    :param stock_code: 股票代码
    :param days_back: 向前追溯的天数
    :param period: 数据周期，默认日线'1d'
    :return: bool 下载是否成功
    """
    print(f"=== 单个股票盘后数据下载 ===")
    print(f"股票代码: {stock_code}")
    print(f"数据周期: {period}")
    print(f"追溯天数: {days_back}天")

    # 计算时间范围
    start_time, end_time = calculate_date_range(days_back)
    print(f"时间范围: {start_time} 到 {end_time}")

    try:
        # 方法1: 使用download_history_data下载单个股票
        print(f"\\n开始下载 {stock_code} 的{period}数据...")
        result = xtdata.download_history_data(
            stock_code=stock_code,
            period=period,
            start_time=start_time,
            end_time=end_time
        )

        if result:
            print(f"✓ {stock_code} 数据下载成功")
            return True
        else:
            print(f"✗ {stock_code} 数据下载失败")
            return False

    except Exception as e:
        print(f"下载过程中出错: {e}")
        return False


def download_multiple_stocks_history(stock_list, days_back, period='1d'):
    """
    使用盘后数据下载方式批量下载多个股票的历史数据
    :param stock_list: 股票代码列表
    :param days_back: 向前追溯的天数
    :param period: 数据周期，默认日线'1d'
    :return: bool 下载是否成功
    """
    print(f"\\n=== 批量股票盘后数据下载 ===")
    print(f"股票列表: {stock_list}")
    print(f"数据周期: {period}")
    print(f"追溯天数: {days_back}天")

    # 计算时间范围
    start_time, end_time = calculate_date_range(days_back)
    print(f"时间范围: {start_time} 到 {end_time}")

    try:
        # 方法2: 使用download_history_data2批量下载
        print(f"\\n开始批量下载数据...")
        xtdata.download_history_data2(
            stock_list=stock_list,
            period=period,
            start_time=start_time,
            end_time=end_time,
            callback=download_progress_callback
        )

        print(f"✓ 批量数据下载完成")
        return True

    except Exception as e:
        print(f"批量下载过程中出错: {e}")
        return False


def download_using_supply_method(stock_list, days_back, period='1d'):
    """
    使用supply_history_data方法下载数据（等价于download_history_data）
    :param stock_list: 股票代码列表
    :param days_back: 向前追溯的天数
    :param period: 数据周期，默认日线'1d'
    """
    print(f"\\n=== 使用Supply方法下载数据 ===")
    print(f"股票列表: {stock_list}")
    print(f"数据周期: {period}")
    print(f"追溯天数: {days_back}天")

    # 计算时间范围
    start_time, end_time = calculate_date_range(days_back)
    print(f"时间范围: {start_time} 到 {end_time}")

    try:
        # supply_history_data是download_history_data的别名
        for stock_code in stock_list:
            print(f"下载 {stock_code} 数据...")
            result = xtdata.supply_history_data(
                stock_code=stock_code,
                period=period,
                start_time=start_time,
                end_time=end_time
            )
            if result:
                print(f"✓ {stock_code} 下载成功")
            else:
                print(f"✗ {stock_code} 下载失败")

        print("Supply方法下载完成")

    except Exception as e:
        print(f"Supply方法下载出错: {e}")


def verify_downloaded_data(stock_code, days_back, period='1d'):
    """
    验证下载的数据
    :param stock_code: 股票代码
    :param days_back: 向前追溯的天数
    :param period: 数据周期
    """
    print(f"\\n=== 验证下载的数据 ===")

    # 计算时间范围
    start_time, end_time = calculate_date_range(days_back)

    try:
        # 使用get_market_data获取下载的数据
        data = xtdata.get_market_data(
            field_list=["time", "open", "high", "low", "close", "volume", "amount"],
            stock_list=[stock_code],
            period=period,
            start_time=start_time,
            end_time=end_time,
            dividend_type='none',
            fill_data=True
        )

        if data and 'close' in data:
            close_data = data['close']
            if stock_code in close_data.columns:
                stock_data = close_data[stock_code].dropna()
                print(f"✓ 成功获取 {stock_code} 数据")
                print(f"数据条数: {len(stock_data)}")
                print(f"数据时间范围: {stock_data.index[0]} 到 {stock_data.index[-1]}")
                print(f"最新收盘价: {stock_data.iloc[-1]:.3f}")

                # 显示最近5天的数据
                print("\\n最近5个交易日数据:")
                recent_data = stock_data.tail(5)
                for date, price in recent_data.items():
                    print(f"  {str(date)[:10]}: {price:.3f}")

                return True
            else:
                print(f"✗ 在数据中未找到股票 {stock_code}")
                return False
        else:
            print("✗ 未获取到有效数据")
            return False

    except Exception as e:
        print(f"验证数据时出错: {e}")
        return False


def download_recent_days_data(stock_codes, days_back=100, period='1d', download_method='batch'):
    """
    主函数：下载最近指定天数的日线数据
    :param stock_codes: 股票代码或股票代码列表
    :param days_back: 向前追溯的天数，默认100天
    :param period: 数据周期，默认日线'1d'
    :param download_method: 下载方法 'single', 'batch', 'supply'
    """
    print("=" * 60)
    print("盘后数据下载程序")
    print("=" * 60)

    # 确保stock_codes是列表格式
    if isinstance(stock_codes, str):
        stock_list = [stock_codes]
    else:
        stock_list = stock_codes

    print(f"下载参数:")
    print(f"  股票代码: {stock_list}")
    print(f"  追溯天数: {days_back}天")
    print(f"  数据周期: {period}")
    print(f"  下载方法: {download_method}")

    success = False

    # 根据选择的方法下载数据
    if download_method == 'single':
        # 单个股票下载方式
        for stock_code in stock_list:
            success = download_single_stock_history(stock_code, days_back, period)
            if not success:
                break

    elif download_method == 'batch':
        # 批量下载方式
        success = download_multiple_stocks_history(stock_list, days_back, period)

    elif download_method == 'supply':
        # Supply方法下载
        download_using_supply_method(stock_list, days_back, period)
        success = True

    else:
        print(f"不支持的下载方法: {download_method}")
        return

    # 验证下载结果
    if success:
        print("\\n开始验证下载的数据...")
        for stock_code in stock_list[:2]:  # 只验证前两个股票，避免输出过多
            verify_downloaded_data(stock_code, days_back, period)

    print("\\n" + "=" * 60)
    print("数据下载程序执行完成")
    print("=" * 60)


def main():
    """主函数示例"""

    # 示例1: 下载单个股票最近100天的日线数据
    print("示例1: 下载单个股票最近100天日线数据")
    download_recent_days_data(
        stock_codes="000001.SZ",
        days_back=100,
        period='1d',
        download_method='single'
    )

    time.sleep(2)  # 等待一下

    # 示例2: 批量下载多个股票最近50天的日线数据
    print("\\n" + "=" * 80)
    print("示例2: 批量下载多个股票最近50天日线数据")
    download_recent_days_data(
        stock_codes=["000001.SZ", "000002.SZ", "600000.SH"],
        days_back=50,
        period='1d',
        download_method='batch'
    )

    time.sleep(2)  # 等待一下

    # 示例3: 下载最近30天的60分钟数据
    print("\\n" + "=" * 80)
    print("示例3: 下载最近30天60分钟数据")
    download_recent_days_data(
        stock_codes=["000001.SZ"],
        days_back=30,
        period='1h',  # 60分钟数据
        download_method='batch'
    )


if __name__ == "__main__":
    # 可以直接调用main()运行示例
    # main()

    # 或者自定义参数运行
    # 修改这里的参数来下载您需要的数据

    # 下载最近100天的日线数据
    download_recent_days_data(
        stock_codes=["000001.SZ", "600519.SH"],  # 可以修改为您需要的股票代码
        days_back=100,  # 可以修改为您需要的天数
        period='1d',  # 可以修改为'1h', '30m', '5m'等
        download_method='batch'  # 推荐使用'batch'批量下载
    )
    print('1')