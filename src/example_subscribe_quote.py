from xtquant import xtdata
import time


# 1. 定义行情回调函数
def quote_callback(datas):
    """
    行情数据回调函数
    :param datas: {stock_code: [data1, data2, ...]} 数据字典
    """
    print("=== 收到行情数据 ===")
    for stock_code, data_list in datas.items():
        print(f"股票代码: {stock_code}")
        for data in data_list:
            if isinstance(data, dict):
                # 打印主要字段
                time_str = data.get('time', 'N/A')
                last_price = data.get('lastPrice', data.get('close', 'N/A'))
                volume = data.get('volume', 'N/A')
                print(f"  时间: {time_str}, 最新价: {last_price}, 成交量: {volume}")
            else:
                print(f"  数据: {data}")
        print("-" * 40)


# 2. 订阅单个股票的实时行情
print("=== 订阅单个股票实时行情 ===")
stock_code = "000001.SZ"
seq1 = xtdata.subscribe_quote(
    stock_code=stock_code,
    period='tick',  # 分笔数据
    start_time='',  # 空表示从当前时间开始
    end_time='',  # 空表示持续订阅
    count=0,  # 0表示持续订阅
    callback=quote_callback
)
print(f"订阅 {stock_code} 分笔行情，订阅号: {seq1}")

# 3. 订阅1分钟K线数据
seq2 = xtdata.subscribe_quote(
    stock_code=stock_code,
    period='1m',  # 1分钟K线
    start_time='',
    end_time='',
    count=0,
    callback=quote_callback
)
print(f"订阅 {stock_code} 1分钟K线，订阅号: {seq2}")

# 4. 订阅全市场数据（整个市场的行情推送）
print("\n=== 订阅全市场数据 ===")


def whole_market_callback(datas):
    """全市场数据回调"""
    print(f"收到全市场数据，股票数量: {len(datas)}")
    # 只打印前几个股票的数据，避免输出过多
    count = 0
    for stock_code, data in datas.items():
        if count < 3:  # 只显示前3个
            last_price = data.get('lastPrice', 'N/A')
            print(f"  {stock_code}: 最新价 {last_price}")
        count += 1
        if count >= 3:
            break
    print("...")


# 订阅沪深两市全推数据
seq3 = xtdata.subscribe_whole_quote(
    code_list=["SH", "SZ"],  # 沪市和深市
    callback=whole_market_callback
)
print(f"订阅全市场数据，订阅号: {seq3}")

# 5. 运行一段时间后取消订阅
print("\n=== 运行订阅 ===")
print("订阅已启动，将运行30秒...")
print("按 Ctrl+C 可以提前退出")

# try:
#     # 使用xtdata.run()来保持连接并接收回调
#     # 这里为了演示，我们只运行30秒
#     start_time = time.time()
#     while time.time() - start_time < 30:
#         time.sleep(1)
#         # 检查连接状态
#         client = xtdata.get_client()
#         if not client.is_connected():
#             print("行情服务连接断开!")
#             break
#
# except KeyboardInterrupt:
#     print("\n用户中断订阅")
xtdata.run()
# 6. 取消订阅
print("\n=== 取消订阅 ===")
xtdata.unsubscribe_quote(seq1)
print(f"取消订阅 {seq1}")

xtdata.unsubscribe_quote(seq2)
print(f"取消订阅 {seq2}")

xtdata.unsubscribe_quote(seq3)
print(f"取消订阅 {seq3}")

print("所有订阅已取消")

# 7. 持续运行的方式（用于生产环境）
print("\n=== 持续运行示例 ===")
print("如果要让程序持续运行接收行情，可以使用:")
print("xtdata.run()  # 这会阻塞线程，持续接收行情回调")
print("注意：run()函数会一直阻塞，直到连接断开")