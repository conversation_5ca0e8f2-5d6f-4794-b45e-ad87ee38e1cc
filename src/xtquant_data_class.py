from xtquant import xtdata

class xtdata_class():
    def __init__(self):
        pass
    def f(data):
        print(data)

    def request_xtdata(self, stock_list : list, period: str):
        """
        stock:股票代码,可以是一个list，也可以是一只单独的股票
        period:数据周期
        return: data_df: df
        """
        xtdata.download_history_data2(stock_list, period = period) # 下载历史数据
        # 获取前需要先订阅，该数据通过指定period = 'limitupperformance'获取
        xtdata.subscribe_quote(stock_list, period = period, count = -1)
        data_df = xtdata.get_market_data_ex([],[stock_list],period=period,
                                            start_time = "", end_time = "",count = 100)
        return data_df

    def request_sql(self, stock_list : list, period: str):
        pass

if __name__ == "__main__":
    xda = xtdata_class()
    data = xda.request_xtdata("000001.SZ", "1d")
    print(data)
