from xtquant import xtdata
import pymysql
import pandas as pd
from typing import Union, List
import logging

class xtdata_class():
    def __init__(self):
        # 数据库配置
        self.db_config = {
            'host': 'localhost',  # 数据库主机地址
            'port': 3306,         # 数据库端口
            'user': 'root',       # 数据库用户名
            'password': '',       # 数据库密码
            'database': 'stock_data',  # 数据库名
            'charset': 'utf8mb4'
        }
        self.connection = None

    def set_database_config(self, host='localhost', port=3306, user='root',
                           password='', database='stock_data', charset='utf8mb4'):
        """
        设置数据库连接配置

        Args:
            host: 数据库主机地址
            port: 数据库端口
            user: 数据库用户名
            password: 数据库密码
            database: 数据库名
            charset: 字符集
        """
        self.db_config = {
            'host': host,
            'port': port,
            'user': user,
            'password': password,
            'database': database,
            'charset': charset
        }
    def f(data):
        print(data)

    def request_xtdata(self, stock_list : list, period: str):
        """
        stock:股票代码,可以是一个list，也可以是一只单独的股票
        period:数据周期
        return: data_df: df
        """
        xtdata.download_history_data2(stock_list, period = period) # 下载历史数据
        # 获取前需要先订阅，该数据通过指定period = 'limitupperformance'获取
        xtdata.subscribe_quote(stock_list, period = period, count = -1)
        data_df = xtdata.get_market_data_ex([],[stock_list],period=period,
                                            start_time = "", end_time = "",count = 100)
        return data_df

    def connect_database(self):
        """连接MySQL数据库"""
        try:
            self.connection = pymysql.connect(**self.db_config)
            logging.info("数据库连接成功")
            return True
        except Exception as e:
            logging.error(f"数据库连接失败: {e}")
            return False

    def close_database(self):
        """关闭数据库连接"""
        if self.connection:
            self.connection.close()
            self.connection = None
            logging.info("数据库连接已关闭")

    def request_sql(self, stock_list: Union[str, List[str]], period: str):
        """
        从MySQL数据库中查询股票数据

        Args:
            stock_list: 股票代码，可以是单个股票代码(str)或股票代码列表(list[str])
            period: 数据周期 (如 '1d', '1h', '5m' 等)

        Returns:
            pandas.DataFrame: 查询到的股票数据，如果查询失败返回None
        """
        # 处理输入参数，统一转换为列表
        if isinstance(stock_list, str):
            stock_codes = [stock_list]
        elif isinstance(stock_list, list):
            stock_codes = stock_list
        else:
            logging.error("stock_list参数类型错误，必须是str或list[str]")
            return None

        if not stock_codes:
            logging.error("股票代码列表不能为空")
            return None

        # 连接数据库
        if not self.connect_database():
            return None

        try:
            # 构建SQL查询语句
            # 假设数据库表结构为: stock_data (stock_code, period, timestamp, open, high, low, close, volume, amount)
            placeholders = ','.join(['%s'] * len(stock_codes))
            sql = f"""
            SELECT stock_code, timestamp, open, high, low, close, volume, amount
            FROM stock_data
            WHERE stock_code IN ({placeholders}) AND period = %s
            ORDER BY stock_code, timestamp
            """

            # 执行查询
            with self.connection.cursor() as cursor:
                cursor.execute(sql, stock_codes + [period])
                results = cursor.fetchall()

                if not results:
                    logging.warning(f"未查询到股票 {stock_codes} 在周期 {period} 的数据")
                    return None

                # 获取列名
                columns = [desc[0] for desc in cursor.description]

                # 转换为DataFrame
                df = pd.DataFrame(results, columns=columns)

                # 设置时间戳为索引
                if 'timestamp' in df.columns:
                    df['timestamp'] = pd.to_datetime(df['timestamp'])
                    df.set_index('timestamp', inplace=True)

                logging.info(f"成功查询到 {len(df)} 条数据")
                return df

        except Exception as e:
            logging.error(f"查询数据库失败: {e}")
            return None
        finally:
            self.close_database()

if __name__ == "__main__":
    xda = xtdata_class()

    # 测试xtquant数据获取
    print("=== 测试xtquant数据获取 ===")
    data = xda.request_xtdata("000001.SZ", "1d")
    print(data)

    # 测试SQL数据库查询
    print("\n=== 测试SQL数据库查询 ===")

    # 设置数据库配置（请根据实际情况修改）
    xda.set_database_config(
        host='localhost',
        port=3306,
        user='root',
        password='your_password',  # 请替换为实际密码
        database='stock_data'      # 请替换为实际数据库名
    )

    # 测试单个股票查询
    print("查询单个股票:")
    sql_data = xda.request_sql("000001.SZ", "1d")
    if sql_data is not None:
        print(sql_data.head())

    # 测试多个股票查询
    print("\n查询多个股票:")
    sql_data_multi = xda.request_sql(["000001.SZ", "000002.SZ", "600000.SH"], "1d")
    if sql_data_multi is not None:
        print(sql_data_multi.head())
