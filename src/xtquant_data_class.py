from xtquant import xtdata
import pymysql
import pandas as pd
from typing import Union, List
import logging

class xtdata_class():
    def __init__(self):
        # 数据库配置
        self.db_config = {
            'host': '**************',  # 数据库主机地址
            'port': 3306,         # 数据库端口
            'user': 'ken',       # 数据库用户名
            'password': 'li173312',       # 数据库密码
            'database': 'ths_stock',  # 数据库名
            'charset': 'utf8mb4'
        }
        self.connection = None
        # 默认表名配置
        self.table_name = 'status_signal'  # 默认股票数据表名

    def set_database_config(self, host='localhost', port=3306, user='root',
                           password='', database='stock_data', charset='utf8mb4'):
        """
        设置数据库连接配置

        Args:
            host: 数据库主机地址
            port: 数据库端口
            user: 数据库用户名
            password: 数据库密码
            database: 数据库名
            charset: 字符集
        """
        self.db_config = {
            'host': host,
            'port': port,
            'user': user,
            'password': password,
            'database': database,
            'charset': charset
        }

    def set_table_name(self, table_name: str):
        """
        设置要查询的数据库表名

        Args:
            table_name: 数据库表名，如 'stock_data', 'daily_data', 'minute_data' 等
        """
        self.table_name = table_name
    def f(data):
        print(data)

    def request_xtdata(self, stock_list : list, period: str):
        """
        stock:股票代码,可以是一个list，也可以是一只单独的股票
        period:数据周期
        return: data_df: df
        """
        xtdata.download_history_data2(stock_list, period = period) # 下载历史数据
        # 获取前需要先订阅，该数据通过指定period = 'limitupperformance'获取
        xtdata.subscribe_quote(stock_list, period = period, count = -1)
        data_df = xtdata.get_market_data_ex([],[stock_list],period=period,
                                            start_time = "", end_time = "",count = 100)
        return data_df

    def connect_database(self):
        """连接MySQL数据库"""
        try:
            self.connection = pymysql.connect(**self.db_config)
            logging.info("数据库连接成功")
            return True
        except Exception as e:
            logging.error(f"数据库连接失败: {e}")
            return False

    def close_database(self):
        """关闭数据库连接"""
        if self.connection:
            self.connection.close()
            self.connection = None
            logging.info("数据库连接已关闭")

    def request_sql(self, stock_list: List[str], keyfeild: str, table_name: str = None):
        """
        从MySQL数据库中查询股票数据

        Args:
            stock_list: 股票代码，可以是单个股票代码(str)或股票代码列表(list[str])
            period: 数据周期 (如 '1d', '1h', '5m' 等)
            table_name: 可选，指定要查询的表名。如果不指定，使用默认表名

        Returns:
            pandas.DataFrame: 查询到的股票数据，如果查询失败返回None
        """
        # 处理输入参数，统一转换为列表
        if isinstance(stock_list, str):
            stock_codes = [stock_list]
        elif isinstance(stock_list, list):
            stock_codes = stock_list
        else:
            logging.error("stock_list参数类型错误，必须是str或list[str]")
            return None

        if not stock_codes:
            logging.error("股票代码列表不能为空")
            return None

        # 确定要使用的表名
        current_table_name = table_name if table_name else self.table_name

        # 连接数据库
        if not self.connect_database():
            return None

        try:
            # 构建SQL查询语句
            # 假设数据库表结构为: stock_data (stock_code, period, timestamp, open, high, low, close, volume, amount)
            placeholders = ','.join(['%s'] * len(stock_codes))
            sql = f"""
            SELECT {stock_list, keyfeild}
            FROM {table_name}
            WHERE stock_code IN ({placeholders}) AND period = %s
            ORDER BY stock_code, timestamp
            """

            # 执行查询
            with self.connection.cursor() as cursor:
                cursor.execute(sql, stock_codes + [keyfeild])
                results = cursor.fetchall()

                if not results:
                    logging.warning(f"未查询到股票 {stock_codes} 在周期 {keyfeild} 的数据")
                    return None

                # 获取列名
                columns = [desc[0] for desc in cursor.description]

                # 转换为DataFrame
                df = pd.DataFrame(results, columns=columns)

                # 设置时间戳为索引
                if 'timestamp' in df.columns:
                    df['timestamp'] = pd.to_datetime(df['timestamp'])
                    df.set_index('timestamp', inplace=True)

                logging.info(f"成功查询到 {len(df)} 条数据")
                return df

        except Exception as e:
            logging.error(f"查询数据库失败: {e}")
            return None
        finally:
            self.close_database()

class UnitTestClass(xtdata_class):
    def __init__(self):
        xtdata_class.__init__(self)

    def unit_test_xtdata_last100(self):
        # 测试xtquant数据获取
        print("=== 测试xtquant数据获取 ===")
        data = self.request_xtdata("000001.SZ", "")
        print(data)

    def unit_test_sql(self):
        # 测试SQL数据库查询
        print("\n=== 测试SQL数据库查询 ===")

        # 设置数据库配置（请根据实际情况修改）
        self.set_database_config(
            host='**************',
            port=3306,
            user='ken',
            password='li173312',  # 请替换为实际密码
            database='ths_stock'  # 请替换为实际数据库名
        )

        # 设置默认表名（可选）
        self.set_table_name('status_signal')  # 设置默认表名

        # 测试单个股票查询（使用默认表名）
        print("查询单个股票（使用默认表名）:")
        sql_data = self.request_sql("000158", "LD_flag_12", table_name="status_signal")
        if sql_data is not None:
            print(sql_data.head())

        # 测试多个股票查询（指定表名）
        print("\n查询多个股票（指定表名）:")
        sql_data_multi = self.request_sql(["000158", "000158.SZ", "000158.SH"], "LD_flag_12", table_name="status_signal")
        if sql_data_multi is not None:
            print(sql_data_multi.head())

        # 测试查询不同表的数据
        print("\n查询分钟数据表:")
        minute_data = self.request_sql("000158", "LD_flag_12", table_name="status_signal")
        if minute_data is not None:
            print(minute_data.head())


if __name__ == "__main__":

    utc = UnitTestClass()
    utc.unit_test_sql()