from pyperclip import init_qt_clipboard

from qmt_trader import StockTradingDemo, TradingCallback
from xtquant_data_class import xtdata_class
from .strategy.macd_strategy import MACDStrategy

"""
QMT 联合 THS的主程序
有以下几个类：
1. qmt_trader: 封装了qmt的交易功能
2. xtquant_data_class: 封装了xtquant的数据获取功能
3. ths_data_class: 封装了ths的数据获取功能
"""

class MainTrader(xtdata_class, StockTradingDemo, MACDStrategy):
    def __init__(self):
        xtdata_class.__init__()
        StockTradingDemo.__init__()
        MACDStrategy.__init__()
        self.init_qmt_trader()

    def init_qmt_trader(self):
        """主函数"""
        # 配置参数
        ACCOUNT_ID = "**********"  # 请替换为你的实际资金账号
        CLIENT_PATH = r"D:\Program\国金证券QMT交易端\userdata_mini"  # 请替换为实际的客户端userdata路径
        SESSION_ID = 1  # 会话ID，可以使用默认值1
        # 创建并运行交易Demo
        self.trader_demo = StockTradingDemo(ACCOUNT_ID, CLIENT_PATH, SESSION_ID)

    def run(self):



if __name__ == "__main__":
    pass