from xtquant import xtdata
import datetime

# 获取当前日期
today = datetime.date.today()
# 计算三天前的日期
three_days_ago = today - datetime.timedelta(days=3)

# 格式化时间
start_time = three_days_ago.strftime('%Y%m%d')
end_time = today.strftime('%Y%m%d')

# 股票代码列表
stock_list = ["000001.SZ", "000002.SZ", "600000.SH"]  # 可根据需要修改

# 字段列表（可选，空列表表示获取全部字段）
field_list = ["time", "open", "high", "low", "close", "volume", "amount"]

print(f"下载时间范围: {start_time} 到 {end_time}")

# 1. 下载日线数据
print("开始下载日线数据...")
daily_data = xtdata.get_market_data(
    field_list=field_list,
    stock_list=stock_list,
    period='1d',           # 日线
    start_time=start_time,
    end_time=end_time,
    dividend_type='none',  # 除权类型："none"不除权, "front"前复权, "back"后复权
    fill_data=True
)
print("日线数据下载完成!")

# 2. 下载60分钟数据
print("开始下载60分钟数据...")
hourly_data = xtdata.get_market_data(
    field_list=field_list,
    stock_list=stock_list,
    period='1h',           # 60分钟线
    start_time=start_time,
    end_time=end_time,
    dividend_type='none',
    fill_data=True
)
print("60分钟数据下载完成!")

# 3. 查看数据结构
print("\n=== 日线数据结构 ===")
for field in daily_data.keys():
    print(f"字段: {field}")
    print(f"数据形状: {daily_data[field].shape}")
    print(f"前几行数据:\n{daily_data[field].head()}")
    print("-" * 50)

print("\n=== 60分钟数据结构 ===")
for field in hourly_data.keys():
    print(f"字段: {field}")
    print(f"数据形状: {hourly_data[field].shape}")
    print(f"前几行数据:\n{hourly_data[field].head()}")
    print("-" * 50)