from xtquant import xtdata
import pandas as pd
import time
import datetime
from typing import List, Dict, Optional

def get_latest_market_data(stock_codes: List[str], batch_size: int = 100) -> Dict:
    """
    批量获取股票的最新市场数据（股价、成交量等）
    """
    market_data = {}
    
    try:
        # 分批获取数据以避免单次请求过大
        for i in range(0, len(stock_codes), batch_size):
            batch_codes = stock_codes[i:i + batch_size]
            print(f"正在获取第{i//batch_size + 1}批次市场数据 ({len(batch_codes)}只股票)...")
            
            # 获取最新的收盘价、成交量等数据
            data = xtdata.get_market_data(
                field_list=['close', 'volume', 'amount'],
                stock_list=batch_codes,
                period='1d',
                count=1  # 获取最新一天的数据
            )
            
            if data and 'close' in data:
                close_data = data['close']
                volume_data = data.get('volume', {})
                amount_data = data.get('amount', {})
                
                for stock_code in batch_codes:
                    if stock_code in close_data.index:
                        # 获取最新收盘价
                        latest_close = close_data.loc[stock_code].iloc[-1] if len(close_data.loc[stock_code]) > 0 else None
                        latest_volume = volume_data.loc[stock_code].iloc[-1] if stock_code in volume_data.index and len(volume_data.loc[stock_code]) > 0 else None
                        latest_amount = amount_data.loc[stock_code].iloc[-1] if stock_code in amount_data.index and len(amount_data.loc[stock_code]) > 0 else None
                        
                        if latest_close is not None and not pd.isna(latest_close):
                            market_data[stock_code] = {
                                'close': float(latest_close),
                                'volume': float(latest_volume) if latest_volume is not None and not pd.isna(latest_volume) else 0,
                                'amount': float(latest_amount) if latest_amount is not None and not pd.isna(latest_amount) else 0
                            }
            
            # 添加小的延迟避免请求过快
            time.sleep(0.1)
            
    except Exception as e:
        print(f"获取市场数据失败: {e}")
    
    return market_data

def calculate_market_values(stock_info: Dict, market_data: Dict) -> Dict:
    """
    计算股票的市值信息
    """
    # 获取股票的基本信息（包括总股本、流通股本）
    detail = xtdata.get_instrument_detail(stock_info['full_code'])
    
    if not detail:
        return {
            'current_price': None,
            'total_shares': None,
            'float_shares': None,
            'total_market_value': None,
            'float_market_value': None,
            'volume': None,
            'amount': None
        }
    
    # 从detail中获取股本信息（单位：股）
    total_shares = detail.get('TotalVolume', 0)  # 总股本
    float_shares = detail.get('FloatVolume', 0)  # 流通股本
    
    # 从市场数据中获取当前价格
    current_price = None
    volume = None
    amount = None
    
    if stock_info['full_code'] in market_data:
        current_price = market_data[stock_info['full_code']]['close']
        volume = market_data[stock_info['full_code']]['volume']
        amount = market_data[stock_info['full_code']]['amount']
    
    # 计算市值（单位：元）
    total_market_value = None
    float_market_value = None
    
    if current_price and total_shares:
        total_market_value = current_price * total_shares  # 总市值
        
    if current_price and float_shares:
        float_market_value = current_price * float_shares  # 流通市值
    
    return {
        'current_price': current_price,
        'total_shares': total_shares,
        'float_shares': float_shares,
        'total_market_value': total_market_value,
        'float_market_value': float_market_value,
        'volume': volume,
        'amount': amount
    }

def format_market_value(value: Optional[float]) -> str:
    """
    格式化市值显示（单位：亿元）
    """
    if value is None or value == 0:
        return "N/A"
    
    # 转换为亿元
    value_yi = value / 100000000
    
    if value_yi >= 1000:
        return f"{value_yi:.0f}亿"
    elif value_yi >= 100:
        return f"{value_yi:.0f}亿"
    elif value_yi >= 10:
        return f"{value_yi:.1f}亿"
    else:
        return f"{value_yi:.2f}亿"

def get_qmt_mainboard_stocks_with_market_value() -> List[Dict]:
    """
    使用QMT接口获取深沪两市主板股票列表，包含市值信息
    """
    stocks = []
    
    try:
        # 首先连接到QMT数据源
        xtdata.connect()
        
        print("正在获取板块列表...")
        # 获取所有可用的板块列表
        sector_list = xtdata.get_sector_list()
        print(f"可用板块: {sector_list}")
        
        # 寻找主板相关的板块
        mainboard_sectors = []
        for sector in sector_list:
            if any(keyword in sector for keyword in ['主板', '沪深A股', 'A股', '上证主板', '深证主板']):
                mainboard_sectors.append(sector)
        
        print(f"找到主板相关板块: {mainboard_sectors}")
        
        # 如果找到主板板块，直接使用
        if mainboard_sectors:
            for sector in mainboard_sectors:
                print(f"正在获取板块 '{sector}' 的股票...")
                sector_stocks = xtdata.get_stock_list_in_sector(sector)
                
                for stock_code in sector_stocks:
                    # 获取股票详细信息
                    detail = xtdata.get_instrument_detail(stock_code)
                    if detail and detail.get('InstrumentName'):
                        # 判断是否为主板股票
                        code_only = stock_code.split('.')[0]
                        if code_only.startswith('60'):  # 上海主板
                            stocks.append({
                                'code': code_only,
                                'name': detail['InstrumentName'],
                                'full_code': stock_code,
                                'market': '上海主板',
                                'exchange': detail.get('ExchangeID', 'SH')
                            })
                        elif code_only.startswith('000'):  # 深圳主板
                            stocks.append({
                                'code': code_only,
                                'name': detail['InstrumentName'],
                                'full_code': stock_code,
                                'market': '深圳主板',
                                'exchange': detail.get('ExchangeID', 'SZ')
                            })
        
        # 如果没有找到明确的主板板块，尝试获取沪深A股然后筛选
        if not stocks:
            print("未找到明确的主板板块，尝试获取沪深A股并筛选...")
            
            # 尝试获取沪深A股板块
            a_stock_sectors = ['沪深A股', 'A股', '沪深300', '上证A股', '深证A股']
            
            for sector_name in a_stock_sectors:
                if sector_name in sector_list:
                    print(f"正在从 '{sector_name}' 板块筛选主板股票...")
                    sector_stocks = xtdata.get_stock_list_in_sector(sector_name)
                    
                    for stock_code in sector_stocks:
                        code_only = stock_code.split('.')[0]
                        
                        # 只选择主板股票：60开头(上海主板)，000开头(深圳主板)
                        if code_only.startswith('60') or code_only.startswith('000'):
                            detail = xtdata.get_instrument_detail(stock_code)
                            if detail and detail.get('InstrumentName'):
                                market = '上海主板' if code_only.startswith('60') else '深圳主板'
                                stocks.append({
                                    'code': code_only,
                                    'name': detail['InstrumentName'],
                                    'full_code': stock_code,
                                    'market': market,
                                    'exchange': detail.get('ExchangeID', 'SH' if code_only.startswith('60') else 'SZ')
                                })
                    break  # 找到一个有效的板块就停止
        
    except Exception as e:
        print(f"使用QMT接口获取数据失败: {e}")
        print("请确保:")
        print("1. QMT客户端已启动")
        print("2. 已正确安装xtquant模块")
        print("3. 网络连接正常")
    
    return stocks

def get_all_stocks_by_code_range() -> List[Dict]:
    """
    通过遍历股票代码范围获取主板股票（备用方案）
    """
    stocks = []
    
    try:
        xtdata.connect()
        
        print("正在通过代码范围获取主板股票...")
        
        # 上海主板：600000-605999
        print("获取上海主板股票 (600000-605999)...")
        for i in range(600000, 606000):
            stock_code = f"{i}.SH"
            detail = xtdata.get_instrument_detail(stock_code)
            if detail and detail.get('InstrumentName'):
                stocks.append({
                    'code': str(i),
                    'name': detail['InstrumentName'],
                    'full_code': stock_code,
                    'market': '上海主板',
                    'exchange': 'SH'
                })
        
        # 深圳主板：000001-000999
        print("获取深圳主板股票 (000001-000999)...")
        for i in range(1, 1000):
            stock_code = f"{i:06d}.SZ"
            detail = xtdata.get_instrument_detail(stock_code)
            if detail and detail.get('InstrumentName'):
                stocks.append({
                    'code': f"{i:06d}",
                    'name': detail['InstrumentName'],
                    'full_code': stock_code,
                    'market': '深圳主板',
                    'exchange': 'SZ'
                })
                
    except Exception as e:
        print(f"通过代码范围获取失败: {e}")
    
    return stocks

def save_to_files(stocks_df: pd.DataFrame) -> None:
    """
    保存股票数据到多种格式文件
    """
    # 保存为CSV
    stocks_df.to_csv('qmt_mainboard_stocks_with_market_value.csv', index=False, encoding='utf-8-sig')
    print("数据已保存到 qmt_mainboard_stocks_with_market_value.csv")
    
    # 保存为Excel
    try:
        with pd.ExcelWriter('qmt_mainboard_stocks_with_market_value.xlsx', engine='openpyxl') as writer:
            stocks_df.to_excel(writer, sheet_name='主板股票', index=False)
            
            # 按市场分别保存到不同的工作表
            sh_stocks = stocks_df[stocks_df['market'] == '上海主板']
            sz_stocks = stocks_df[stocks_df['market'] == '深圳主板']
            
            if len(sh_stocks) > 0:
                sh_stocks.to_excel(writer, sheet_name='上海主板', index=False)
            
            if len(sz_stocks) > 0:
                sz_stocks.to_excel(writer, sheet_name='深圳主板', index=False)
        
        print("数据已保存到 qmt_mainboard_stocks_with_market_value.xlsx")
    except ImportError:
        print("未安装openpyxl，无法保存Excel格式，请使用: pip install openpyxl")
    
    # 按市场分别保存CSV
    if len(stocks_df) > 0:
        sh_stocks = stocks_df[stocks_df['market'] == '上海主板']
        sz_stocks = stocks_df[stocks_df['market'] == '深圳主板']
        
        if len(sh_stocks) > 0:
            sh_stocks.to_csv('sh_mainboard_stocks_with_market_value.csv', index=False, encoding='utf-8-sig')
            print(f"上海主板股票({len(sh_stocks)}只)已保存到 sh_mainboard_stocks_with_market_value.csv")
        
        if len(sz_stocks) > 0:
            sz_stocks.to_csv('sz_mainboard_stocks_with_market_value.csv', index=False, encoding='utf-8-sig')
            print(f"深圳主板股票({len(sz_stocks)}只)已保存到 sz_mainboard_stocks_with_market_value.csv")

def main():
    """
    主函数：使用QMT接口获取深沪两市主板股票，包含市值信息
    """
    print("=== 使用QMT接口获取深沪两市主板股票（含市值） ===")
    
    all_stocks = []
    
    # 方法1：通过板块获取（推荐）
    print("\n方法1: 通过板块获取主板股票...")
    all_stocks = get_qmt_mainboard_stocks_with_market_value()
    
    # 方法2：通过代码范围获取（备用）
    if len(all_stocks) == 0:
        print("\n板块方法失败，尝试方法2: 通过代码范围获取...")
        all_stocks = get_all_stocks_by_code_range()
    
    if all_stocks:
        # 去重处理
        seen_codes = set()
        unique_stocks = []
        for stock in all_stocks:
            if stock['code'] not in seen_codes:
                unique_stocks.append(stock)
                seen_codes.add(stock['code'])
        
        print(f"\n获取到 {len(unique_stocks)} 只主板股票，正在获取市值数据...")
        
        # 获取股票代码列表用于批量获取市场数据
        stock_codes = [stock['full_code'] for stock in unique_stocks]
        
        # 批量获取最新市场数据
        market_data = get_latest_market_data(stock_codes)
        print(f"成功获取 {len(market_data)} 只股票的市场数据")
        
        # 为每只股票计算市值信息
        print("正在计算市值信息...")
        enhanced_stocks = []
        
        for i, stock in enumerate(unique_stocks):
            if (i + 1) % 50 == 0:
                print(f"处理进度: {i + 1}/{len(unique_stocks)}")
            
            # 计算市值信息
            market_values = calculate_market_values(stock, market_data)
            
            # 合并所有信息
            enhanced_stock = {
                'code': stock['code'],
                'name': stock['name'],
                'market': stock['market'],
                'full_code': stock['full_code'],
                'exchange': stock['exchange'],
                'current_price': market_values['current_price'],
                'total_shares': market_values['total_shares'],
                'float_shares': market_values['float_shares'],
                'total_market_value': market_values['total_market_value'],
                'float_market_value': market_values['float_market_value'],
                'total_market_value_yi': format_market_value(market_values['total_market_value']),
                'float_market_value_yi': format_market_value(market_values['float_market_value']),
                'volume': market_values['volume'],
                'amount': market_values['amount']
            }
            
            enhanced_stocks.append(enhanced_stock)
        
        # 转换为DataFrame
        df = pd.DataFrame(enhanced_stocks)
        
        # 按总市值排序（从大到小）
        df = df.sort_values('total_market_value', ascending=False, na_position='last').reset_index(drop=True)
        
        # 统计信息
        print(f"\n=== 获取成功！===")
        print(f"共获取到 {len(df)} 只主板股票")
        print("\n各市场统计:")
        market_stats = df['market'].value_counts()
        for market, count in market_stats.items():
            print(f"  {market}: {count} 只")
        
        # 市值统计
        valid_market_values = df['total_market_value'].dropna()
        if len(valid_market_values) > 0:
            total_market_cap = valid_market_values.sum() / 100000000  # 转换为亿元
            avg_market_cap = valid_market_values.mean() / 100000000
            print(f"\n市值统计:")
            print(f"  总市值: {total_market_cap:.0f} 亿元")
            print(f"  平均市值: {avg_market_cap:.1f} 亿元")
            print(f"  有效市值数据: {len(valid_market_values)} 只")
        
        # 保存文件
        print("\n=== 保存文件 ===")
        save_to_files(df)
        
        # 显示样例数据
        print(f"\n=== 按市值排序的前10只股票 ===")
        display_cols = ['code', 'name', 'market', 'current_price', 'total_market_value_yi', 'float_market_value_yi']
        print(df[display_cols].head(10).to_string(index=False))
        
        print(f"\n=== 市值最小的10只股票 ===")
        bottom_10 = df[df['total_market_value'].notna()].tail(10)
        print(bottom_10[display_cols].to_string(index=False))
        
        return df
    else:
        print("\n❌ 获取股票数据失败！")
        print("请检查:")
        print("1. QMT客户端是否正常启动")
        print("2. xtquant模块是否正确安装")
        print("3. 是否有访问行情数据的权限")
        return None

def get_stock_detail_with_market_value(stock_code: str) -> Dict:
    """
    获取单个股票的详细信息，包含市值
    :param stock_code: 股票代码，如 "600000.SH" 或 "600000"
    """
    try:
        xtdata.connect()
        
        # 如果没有交易所后缀，自动添加
        if '.' not in stock_code:
            if stock_code.startswith('60'):
                stock_code = f"{stock_code}.SH"
            elif stock_code.startswith('00'):
                stock_code = f"{stock_code}.SZ"
        
        # 获取基本信息
        detail = xtdata.get_instrument_detail(stock_code)
        
        if detail:
            # 获取市场数据
            market_data = get_latest_market_data([stock_code])
            
            # 计算市值
            stock_info = {'full_code': stock_code}
            market_values = calculate_market_values(stock_info, market_data)
            
            # 合并信息
            result = {**detail, **market_values}
            result['total_market_value_yi'] = format_market_value(market_values['total_market_value'])
            result['float_market_value_yi'] = format_market_value(market_values['float_market_value'])
            
            return result
        
        return {}
        
    except Exception as e:
        print(f"获取股票 {stock_code} 详细信息失败: {e}")
        return {}

if __name__ == "__main__":
    # 安装依赖包（如果需要）
    # pip install xtquant pandas openpyxl
    
    # 运行主程序
    stocks_df = main()
    
    # 可选：获取特定股票的详细信息（包含市值）
    # detail = get_stock_detail_with_market_value("600000")  # 浦发银行
    # print(f"\n浦发银行详细信息:")
    # if detail:
    #     print(f"股票名称: {detail.get('InstrumentName', 'N/A')}")
    #     print(f"当前价格: {detail.get('current_price', 'N/A')} 元")
    #     print(f"总市值: {detail.get('total_market_value_yi', 'N/A')}")
    #     print(f"流通市值: {detail.get('float_market_value_yi', 'N/A')}")