from xtquant import xtdata
import datetime
import pandas as pd


class MACDCalculator:
    """MACD指标计算器 - 自实现版本"""

    def __init__(self, fast_period=12, slow_period=26, signal_period=9):
        """
        初始化MACD参数
        :param fast_period: 快线EMA周期，默认12
        :param slow_period: 慢线EMA周期，默认26
        :param signal_period: 信号线EMA周期，默认9
        """
        self.fast_period = fast_period
        self.slow_period = slow_period
        self.signal_period = signal_period

    def calculate_ema(self, prices, period):
        """
        计算指数移动平均线(EMA)
        :param prices: 价格序列（list或pandas Series）
        :param period: EMA周期
        :return: EMA值列表
        """
        if len(prices) == 0:
            return []

        # 转换为列表便于处理
        if hasattr(prices, 'tolist'):
            prices = prices.tolist()

        ema_values = []
        # 平滑因子
        multiplier = 2.0 / (period + 1)

        # 第一个EMA值使用简单移动平均
        if len(prices) >= period:
            sma = sum(prices[:period]) / period
            ema_values.extend([None] * (period - 1))  # 前面的值设为None
            ema_values.append(sma)

            # 后续EMA值计算
            for i in range(period, len(prices)):
                ema = (prices[i] * multiplier) + (ema_values[-1] * (1 - multiplier))
                ema_values.append(ema)
        else:
            # 数据不足，返回None
            ema_values = [None] * len(prices)

        return ema_values

    def calculate_macd(self, prices):
        """
        计算MACD指标
        :param prices: 收盘价序列
        :return: dict包含macd_line, signal_line, histogram
        """
        if len(prices) < self.slow_period:
            print(f"警告：数据量不足，需要至少{self.slow_period}个数据点")
            return {
                'macd_line': [None] * len(prices),
                'signal_line': [None] * len(prices),
                'histogram': [None] * len(prices)
            }

        # 计算快线和慢线EMA
        fast_ema = self.calculate_ema(prices, self.fast_period)
        slow_ema = self.calculate_ema(prices, self.slow_period)

        # 计算MACD线（DIF线）
        macd_line = []
        for i in range(len(prices)):
            if fast_ema[i] is not None and slow_ema[i] is not None:
                macd_line.append(fast_ema[i] - slow_ema[i])
            else:
                macd_line.append(None)

        # 计算信号线（DEA线）- MACD线的EMA
        # 过滤掉None值来计算信号线
        valid_macd = [x for x in macd_line if x is not None]
        if len(valid_macd) >= self.signal_period:
            signal_ema = self.calculate_ema(valid_macd, self.signal_period)

            # 将信号线对齐到原始长度
            signal_line = [None] * len(macd_line)
            valid_start_idx = next(i for i, x in enumerate(macd_line) if x is not None)

            for i, signal_val in enumerate(signal_ema):
                if signal_val is not None and valid_start_idx + i < len(signal_line):
                    signal_line[valid_start_idx + i] = signal_val
        else:
            signal_line = [None] * len(prices)

        # 计算直方图（MACD - Signal）
        histogram = []
        for i in range(len(prices)):
            if macd_line[i] is not None and signal_line[i] is not None:
                histogram.append(macd_line[i] - signal_line[i])
            else:
                histogram.append(None)

        return {
            'macd_line': macd_line,
            'signal_line': signal_line,
            'histogram': histogram
        }


def download_recent_data(stock_code, count=100):
    """
    下载最近指定数量的日线和60分钟数据
    :param stock_code: 股票代码
    :param count: 数据数量
    :return: (daily_data, hourly_data)
    """
    print(f"开始下载 {stock_code} 最近{count}个周期的数据...")

    # 字段列表
    field_list = ["time", "open", "high", "low", "close", "volume", "amount"]

    try:
        # 下载日线数据
        print("下载日线数据...")
        daily_data = xtdata.get_market_data_ex(
            stock_list=[stock_code],
            period='1d',
            start_time='',  # 空字符串表示从最早开始
            end_time='',  # 空字符串表示到最新
            count=count,  # 最近count个数据
            dividend_type='none',
            fill_data=True
        )
        # 下载60分钟数据
        print("下载60分钟数据...")
        hourly_data = xtdata.get_market_data(
            stock_list=[stock_code],
            period='1h',
            start_time='',
            end_time='',
            count=count,
            dividend_type='none',
            fill_data=True
        )

        print("数据下载完成!")
        return daily_data, hourly_data

    except Exception as e:
        print(f"下载数据时出错: {e}")
        return None, None


def process_market_data(market_data, stock_code, data_type):
    """
    处理市场数据，提取指定股票的数据并转换格式
    :param market_data: 从xtdata获取的市场数据
    :param stock_code: 股票代码
    :param data_type: 数据类型描述（用于日志）
    :return: DataFrame格式的数据
    """
    if not market_data or 'close' not in market_data:
        print(f"错误：{data_type}数据为空或格式不正确")
        return None

    # 创建DataFrame
    df_data = {}
    for field in market_data:
        if stock_code in market_data[field].columns:
            df_data[field] = market_data[field][stock_code].dropna()
        else:
            print(f"警告：在{field}字段中找不到股票{stock_code}")
            continue

    if not df_data:
        print(f"错误：无法提取{stock_code}的{data_type}数据")
        return None

    # 确保所有字段长度一致
    min_length = min(len(df_data[field]) for field in df_data)
    for field in df_data:
        df_data[field] = df_data[field].iloc[-min_length:]

    df = pd.DataFrame(df_data)
    df = df.sort_index()  # 按时间排序

    print(f"{data_type}数据处理完成，共{len(df)}条记录")
    print(f"时间范围：{df.index[0]} 到 {df.index[-1]}")

    return df


def calculate_and_display_macd(df, data_type):
    """
    计算并显示MACD指标
    :param df: 包含价格数据的DataFrame
    :param data_type: 数据类型描述
    """
    if df is None or len(df) == 0:
        print(f"错误：{data_type}数据为空，无法计算MACD")
        return None

    print(f"\n=== 计算{data_type}MACD指标 ===")

    # 创建MACD计算器
    macd_calc = MACDCalculator(fast_period=12, slow_period=26, signal_period=9)

    # 计算MACD
    close_prices = df['close'].tolist()
    macd_result = macd_calc.calculate_macd(close_prices)

    # 将结果添加到DataFrame
    df_result = df.copy()
    df_result['macd_line'] = macd_result['macd_line']
    df_result['signal_line'] = macd_result['signal_line']
    df_result['histogram'] = macd_result['histogram']

    # 显示最近的MACD值
    print("最近10个交易日的MACD指标：")
    print("-" * 100)
    print(f"{'时间':<20} {'收盘价':<10} {'MACD线':<12} {'信号线':<12} {'直方图':<12}")
    print("-" * 100)

    for i in range(max(0, len(df_result) - 10), len(df_result)):
        row = df_result.iloc[i]
        time_str = str(row.name)[:19] if hasattr(row.name, 'strftime') else str(row.name)
        close_val = f"{row['close']:.3f}" if pd.notna(row['close']) else "N/A"
        macd_val = f"{row['macd_line']:.6f}" if pd.notna(row['macd_line']) else "N/A"
        signal_val = f"{row['signal_line']:.6f}" if pd.notna(row['signal_line']) else "N/A"
        hist_val = f"{row['histogram']:.6f}" if pd.notna(row['histogram']) else "N/A"

        print(f"{time_str:<20} {close_val:<10} {macd_val:<12} {signal_val:<12} {hist_val:<12}")

    # 统计信息
    valid_macd = df_result['macd_line'].dropna()
    valid_signal = df_result['signal_line'].dropna()
    valid_hist = df_result['histogram'].dropna()

    print(f"\n{data_type}MACD统计信息：")
    print(f"有效MACD数据点：{len(valid_macd)}")
    print(f"有效信号线数据点：{len(valid_signal)}")
    print(f"有效直方图数据点：{len(valid_hist)}")

    if len(valid_hist) > 0:
        current_hist = valid_hist.iloc[-1]
        prev_hist = valid_hist.iloc[-2] if len(valid_hist) > 1 else None

        print(f"当前直方图值：{current_hist:.6f}")
        if prev_hist is not None:
            trend = "上升" if current_hist > prev_hist else "下降" if current_hist < prev_hist else "持平"
            print(f"直方图趋势：{trend} (较前一期变化：{current_hist - prev_hist:.6f})")

    return df_result


def main():
    """主函数"""
    print("=== 股票数据下载和MACD计算程序 ===")

    # 设置股票代码
    stock_code = "000001.SZ"  # 可以修改为其他股票
    data_count = 100

    print(f"股票代码：{stock_code}")
    print(f"数据数量：最近{data_count}个周期")

    # 下载数据
    daily_data, hourly_data = download_recent_data(stock_code, data_count)

    if daily_data is None or hourly_data is None:
        print("数据下载失败，程序退出")
        return

    # 处理日线数据
    print(f"\n=== 处理日线数据 ===")
    daily_df = process_market_data(daily_data, stock_code, "日线")

    # 处理60分钟数据
    print(f"\n=== 处理60分钟数据 ===")
    hourly_df = process_market_data(hourly_data, stock_code, "60分钟")

    # 计算日线MACD
    daily_macd_df = calculate_and_display_macd(daily_df, "日线")

    # 计算60分钟MACD
    hourly_macd_df = calculate_and_display_macd(hourly_df, "60分钟")

    # 保存结果到CSV文件（可选）
    try:
        if daily_macd_df is not None:
            filename = f"{stock_code}_daily_macd_{datetime.date.today().strftime('%Y%m%d')}.csv"
            daily_macd_df.to_csv(filename, encoding='utf-8-sig')
            print(f"\n日线MACD数据已保存到：{filename}")

        if hourly_macd_df is not None:
            filename = f"{stock_code}_hourly_macd_{datetime.date.today().strftime('%Y%m%d')}.csv"
            hourly_macd_df.to_csv(filename, encoding='utf-8-sig')
            print(f"60分钟MACD数据已保存到：{filename}")

    except Exception as e:
        print(f"保存文件时出错：{e}")

    print("\n=== 程序执行完成 ===")


if __name__ == "__main__":
    main()