from xtquant import  xttrader
from xtquant import xttype
from xtquant import xtconstant
import time
import threading


class TradingCallback(xttrader.XtQuantTraderCallback):
    """交易回调类 - 处理各种交易事件"""

    def __init__(self):
        super().__init__()
        self.order_responses = {}  # 存储订单响应
        self.order_errors = {}  # 存储订单错误
        self.positions = {}  # 存储持仓信息
        self.assets = {}  # 存储资产信息

    def on_connected(self):
        """连接成功回调"""
        print("✓ 交易服务器连接成功!")

    def on_disconnected(self):
        """连接断开回调"""
        print("✗ 交易服务器连接断开!")

    def on_account_status(self, status):
        """账户状态变化回调"""
        print(f"账户状态更新:")
        print(f"  账户类型: {status.account_type}")
        print(f"  账户ID: {status.account_id}")
        print(f"  状态: {status.status}")

    def on_stock_asset(self, asset):
        """资产变化回调"""
        print(f"资产更新:")
        print(f"  账户: {asset.account_id}")
        print(f"  可用资金: {asset.cash:.2f}")
        print(f"  冻结资金: {asset.frozen_cash:.2f}")
        print(f"  持仓市值: {asset.market_value:.2f}")
        print(f"  总资产: {asset.total_asset:.2f}")
        self.assets[asset.account_id] = asset

    def on_stock_order(self, order):
        """委托状态变化回调"""
        print(f"委托状态更新:")
        print(f"  账户: {order.account_id}")
        print(f"  股票代码: {order.stock_code}")
        print(f"  委托编号: {order.order_id}")
        print(f"  委托类型: {'买入' if order.order_type == 23 else '卖出'}")
        print(f"  委托数量: {order.order_volume}")
        print(f"  委托价格: {order.price:.3f}")
        print(f"  成交数量: {order.traded_volume}")
        print(f"  委托状态: {self._get_order_status_desc(order.order_status)}")
        if order.status_msg:
            print(f"  状态信息: {order.status_msg}")

    def on_stock_trade(self, trade):
        """成交回调"""
        print(f"成交通知:")
        print(f"  账户: {trade.account_id}")
        print(f"  股票代码: {trade.stock_code}")
        print(f"  成交编号: {trade.traded_id}")
        print(f"  成交时间: {trade.traded_time}")
        print(f"  成交价格: {trade.traded_price:.3f}")
        print(f"  成交数量: {trade.traded_volume}")
        print(f"  成交金额: {trade.traded_amount:.2f}")

    def on_stock_position(self, position):
        """持仓变化回调"""
        print(f"持仓更新:")
        print(f"  账户: {position.account_id}")
        print(f"  股票代码: {position.stock_code}")
        print(f"  持仓数量: {position.volume}")
        print(f"  可用数量: {position.can_use_volume}")
        print(f"  成本价格: {position.open_price:.3f}")
        print(f"  持仓市值: {position.market_value:.2f}")
        self.positions[f"{position.account_id}_{position.stock_code}"] = position

    def on_order_error(self, order_error):
        """委托错误回调"""
        print(f"✗ 委托失败:")
        print(f"  账户: {order_error.account_id}")
        print(f"  订单编号: {order_error.order_id}")
        print(f"  错误代码: {order_error.error_id}")
        print(f"  错误信息: {order_error.error_msg}")
        self.order_errors[order_error.order_id] = order_error

    def on_cancel_error(self, cancel_error):
        """撤单错误回调"""
        print(f"✗ 撤单失败:")
        print(f"  账户: {cancel_error.account_id}")
        print(f"  订单编号: {cancel_error.order_id}")
        print(f"  错误代码: {cancel_error.error_id}")
        print(f"  错误信息: {cancel_error.error_msg}")

    def on_order_stock_async_response(self, response):
        """异步下单响应回调"""
        print(f"异步下单响应:")
        print(f"  账户: {response.account_id}")
        print(f"  订单编号: {response.order_id}")
        print(f"  请求序号: {response.seq}")
        if response.error_msg:
            print(f"  错误信息: {response.error_msg}")
        self.order_responses[response.seq] = response

    def _get_order_status_desc(self, status):
        """获取委托状态描述"""
        status_map = {
            48: "未报",
            49: "待报",
            50: "已报",
            51: "已报待撤",
            52: "部成待撤",
            53: "部撤",
            54: "已撤",
            55: "部成",
            56: "已成",
            57: "废单",
            255: "未知"
        }
        return status_map.get(status, f"状态码:{status}")


class StockTradingDemo:
    """股票交易Demo类"""

    def __init__(self, account_id, client_path, session_id=1):
        """
        初始化交易Demo
        :param account_id: 资金账号
        :param client_path: 迅投极速交易客户端安装路径下的userdata文件夹路径
        :param session_id: 会话ID，默认为1
        """
        self.account_id = account_id
        self.client_path = client_path
        self.session_id = session_id
        self.trader = None
        self.callback = None
        self.account = None

    def initialize(self):
        """初始化交易环境"""
        print("=== 初始化股票交易Demo ===")

        try:
            # 1. 创建交易客户端
            self.trader = xttrader.XtQuantTrader(self.client_path, self.session_id)

            # 2. 创建回调处理器
            self.callback = TradingCallback()

            # 3. 注册回调
            self.trader.register_callback(self.callback)

            # 4. 启动交易客户端
            self.trader.start()

            # 5. 连接交易服务器
            connect_result = self.trader.connect()
            if connect_result != 0:
                print(f"✗ 连接交易服务器失败，错误码: {connect_result}")
                return False

            # 6. 创建证券账户对象
            self.account = xttype.StockAccount(self.account_id, 'STOCK')

            # 7. 订阅账户推送
            self.trader.subscribe(self.account)

            print(f"✓ 交易环境初始化成功")
            print(f"✓ 账户: {self.account_id}")

            # 等待连接稳定
            time.sleep(2)

            return True

        except Exception as e:
            print(f"✗ 初始化失败: {e}")
            return False

    def query_account_info(self):
        """查询账户信息"""
        print("\\n=== 查询账户信息 ===")

        try:
            # 查询账户状态
            account_status = self.trader.query_account_status()
            if account_status:
                for status in account_status:
                    print(f"账户: {status.account_id}, 状态: {status.status}")

            # 查询资产
            asset = self.trader.query_stock_asset(self.account)
            if asset:
                print(f"账户资产:")
                print(f"  可用资金: {asset.cash:.2f}")
                print(f"  冻结资金: {asset.frozen_cash:.2f}")
                print(f"  持仓市值: {asset.market_value:.2f}")
                print(f"  总资产: {asset.total_asset:.2f}")
                return asset
            else:
                print("✗ 查询资产失败")
                return None

        except Exception as e:
            print(f"✗ 查询账户信息失败: {e}")
            return None

    def query_positions(self):
        """查询持仓"""
        print("\\n=== 查询持仓 ===")

        try:
            positions = self.trader.query_stock_positions(self.account)
            if positions:
                print(f"持仓列表 (共{len(positions)}只):")
                for pos in positions:
                    profit_loss = (pos.market_value - pos.open_price * pos.volume) if pos.volume > 0 else 0
                    profit_rate = (profit_loss / (
                                pos.open_price * pos.volume) * 100) if pos.volume > 0 and pos.open_price > 0 else 0

                    print(f"  {pos.stock_code}:")
                    print(f"    持仓: {pos.volume}股 (可用: {pos.can_use_volume})")
                    print(f"    成本: {pos.open_price:.3f}")
                    print(f"    市值: {pos.market_value:.2f}")
                    print(f"    盈亏: {profit_loss:.2f} ({profit_rate:.2f}%)")
                return positions
            else:
                print("暂无持仓")
                return []

        except Exception as e:
            print(f"✗ 查询持仓失败: {e}")
            return []

    def buy_stock(self, stock_code, volume, price, price_type=xtconstant.FIX_PRICE):
        """
        买入股票
        :param stock_code: 股票代码，如 "000001.SZ"
        :param volume: 买入数量（股）
        :param price: 买入价格
        :param price_type: 价格类型，默认限价
        :return: 订单ID
        """
        print(f"\\n=== 买入股票 ===")
        print(f"股票代码: {stock_code}")
        print(f"买入数量: {volume}股")
        print(f"买入价格: {price:.3f}")

        try:
            # 下买单
            order_id = self.trader.order_stock(
                account=self.account,
                stock_code=stock_code,
                order_type=xtconstant.STOCK_BUY,  # 23: 买入
                order_volume=volume,
                price_type=price_type,
                price=price,
                strategy_name="Demo策略",
                order_remark="API买入测试"
            )

            if order_id > 0:
                print(f"✓ 买入委托成功，订单号: {order_id}")
                return order_id
            else:
                print(f"✗ 买入委托失败，返回值: {order_id}")
                return None

        except Exception as e:
            print(f"✗ 买入操作失败: {e}")
            return None

    def sell_stock(self, stock_code, volume, price, price_type=xtconstant.FIX_PRICE):
        """
        卖出股票
        :param stock_code: 股票代码，如 "000001.SZ"
        :param volume: 卖出数量（股）
        :param price: 卖出价格
        :param price_type: 价格类型，默认限价
        :return: 订单ID
        """
        print(f"\\n=== 卖出股票 ===")
        print(f"股票代码: {stock_code}")
        print(f"卖出数量: {volume}股")
        print(f"卖出价格: {price:.3f}")

        try:
            # 下卖单
            order_id = self.trader.order_stock(
                account=self.account,
                stock_code=stock_code,
                order_type=xtconstant.STOCK_SELL,  # 24: 卖出
                order_volume=volume,
                price_type=price_type,
                price=price,
                strategy_name="Demo策略",
                order_remark="API卖出测试"
            )

            if order_id > 0:
                print(f"✓ 卖出委托成功，订单号: {order_id}")
                return order_id
            else:
                print(f"✗ 卖出委托失败，返回值: {order_id}")
                return None

        except Exception as e:
            print(f"✗ 卖出操作失败: {e}")
            return None

    def cancel_order(self, order_id):
        """
        撤销订单
        :param order_id: 订单ID
        :return: 撤单结果
        """
        print(f"\\n=== 撤销订单 ===")
        print(f"订单号: {order_id}")

        try:
            result = self.trader.cancel_order_stock(self.account, order_id)

            if result == 0:
                print(f"✓ 撤单成功")
                return True
            else:
                error_msg = {
                    -1: "委托已完成，撤单失败",
                    -2: "未找到对应委托编号，撤单失败",
                    -3: "账号未登录，撤单失败"
                }.get(result, f"撤单失败，错误码: {result}")
                print(f"✗ {error_msg}")
                return False

        except Exception as e:
            print(f"✗ 撤单操作失败: {e}")
            return False

    def query_orders(self, cancelable_only=False):
        """
        查询当日委托
        :param cancelable_only: 是否只查询可撤委托
        :return: 委托列表
        """
        print(f"\\n=== 查询委托 ===")

        try:
            orders = self.trader.query_stock_orders(self.account, cancelable_only)
            if orders:
                print(f"委托列表 (共{len(orders)}笔):")
                for order in orders:
                    print(f"  订单{order.order_id}:")
                    print(f"    股票: {order.stock_code}")
                    print(f"    类型: {'买入' if order.order_type == 23 else '卖出'}")
                    print(f"    数量: {order.order_volume}股")
                    print(f"    价格: {order.price:.3f}")
                    print(f"    成交: {order.traded_volume}股")
                    print(f"    状态: {self.callback._get_order_status_desc(order.order_status)}")
                return orders
            else:
                print("暂无委托记录")
                return []

        except Exception as e:
            print(f"✗ 查询委托失败: {e}")
            return []

    def query_trades(self):
        """查询当日成交"""
        print(f"\\n=== 查询成交 ===")

        try:
            trades = self.trader.query_stock_trades(self.account)
            if trades:
                print(f"成交列表 (共{len(trades)}笔):")
                for trade in trades:
                    print(f"  成交{trade.traded_id}:")
                    print(f"    股票: {trade.stock_code}")
                    print(f"    类型: {'买入' if trade.order_type == 23 else '卖出'}")
                    print(f"    价格: {trade.traded_price:.3f}")
                    print(f"    数量: {trade.traded_volume}股")
                    print(f"    金额: {trade.traded_amount:.2f}")
                    print(f"    时间: {trade.traded_time}")
                return trades
            else:
                print("暂无成交记录")
                return []

        except Exception as e:
            print(f"✗ 查询成交失败: {e}")
            return []

    def run_demo(self):
        """运行交易Demo"""
        print("=== 股票交易API Demo ===")

        # 初始化
        if not self.initialize():
            return

        try:
            # 查询账户信息
            asset = self.query_account_info()

            # 查询持仓
            positions = self.query_positions()

            # 查询委托
            orders = self.query_orders()

            # 查询成交
            trades = self.query_trades()

            # Demo交易操作（请根据实际情况修改）
            print("\\n=== Demo交易操作 ===")
            print("注意：以下为演示代码，实际使用时请谨慎操作!")

            # 示例：买入平安银行
            order_id = self.buy_stock("513050.SH", 100, 1.489)
            if order_id:
                time.sleep(3)  # 等待一段时间
                # 查询订单状态
                self.query_orders()
                # 如果需要撤单
                # self.cancel_order(order_id)

            print("\\nDemo运行完成，程序将持续运行以接收回调...")
            print("按 Ctrl+C 退出程序")

            # 保持程序运行以接收回调
            while True:
                time.sleep(1)

        except KeyboardInterrupt:
            print("\\n用户中断程序")
        except Exception as e:
            print(f"\\n程序运行错误: {e}")
        finally:
            self.cleanup()

    def cleanup(self):
        """清理资源"""
        print("\\n=== 清理资源 ===")
        try:
            if self.trader:
                # 取消订阅
                if self.account:
                    self.trader.unsubscribe(self.account)
                # 停止交易客户端
                self.trader.stop()
            print("✓ 资源清理完成")
        except Exception as e:
            print(f"✗ 资源清理失败: {e}")


def main():
    """主函数"""
    # 配置参数
    ACCOUNT_ID = "**********"  # 请替换为你的实际资金账号
    CLIENT_PATH = r"D:\国金证券QMT交易端\userdata_mini"  # 请替换为实际的客户端userdata路径
    SESSION_ID = 1  # 会话ID，可以使用默认值1

    if ACCOUNT_ID == "your_account_id":
        print("请先配置正确的参数!")
        print("需要配置:")
        print("1. ACCOUNT_ID - 你的资金账号")
        print("2. CLIENT_PATH - 迅投极速交易客户端安装路径下的userdata文件夹路径")
        print("   例如: C:\\Program Files\\XTTrader\\userdata_mini")
        print("   或者: D:\\迅投极速交易\\userdata_mini")
        return

    # 创建并运行交易Demo
    demo = StockTradingDemo(ACCOUNT_ID, CLIENT_PATH, SESSION_ID)
    demo.run_demo()


if __name__ == "__main__":
    main()

