-- 股票数据库表结构示例
-- 请根据实际需求调整表结构

-- 创建数据库
CREATE DATABASE IF NOT EXISTS stock_data CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE stock_data;

-- 创建股票数据表
CREATE TABLE IF NOT EXISTS stock_data (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    stock_code VARCHAR(20) NOT NULL COMMENT '股票代码，如 000001.SZ',
    period VARCHAR(10) NOT NULL COMMENT '数据周期，如 1d, 1h, 5m',
    timestamp DATETIME NOT NULL COMMENT '时间戳',
    open DECIMAL(10,3) NOT NULL COMMENT '开盘价',
    high DECIMAL(10,3) NOT NULL COMMENT '最高价',
    low DECIMAL(10,3) NOT NULL COMMENT '最低价',
    close DECIMAL(10,3) NOT NULL COMMENT '收盘价',
    volume BIGINT DEFAULT 0 COMMENT '成交量',
    amount DECIMAL(20,2) DEFAULT 0 COMMENT '成交额',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 创建索引
    INDEX idx_stock_period_time (stock_code, period, timestamp),
    INDEX idx_timestamp (timestamp),
    INDEX idx_stock_code (stock_code),
    
    -- 创建唯一约束，防止重复数据
    UNIQUE KEY uk_stock_period_time (stock_code, period, timestamp)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='股票行情数据表';

-- 插入示例数据
INSERT INTO stock_data (stock_code, period, timestamp, open, high, low, close, volume, amount) VALUES
('000001.SZ', '1d', '2024-01-01 09:30:00', 10.50, 10.80, 10.30, 10.75, 1000000, 10750000.00),
('000001.SZ', '1d', '2024-01-02 09:30:00', 10.75, 11.00, 10.60, 10.90, 1200000, 13080000.00),
('000001.SZ', '1d', '2024-01-03 09:30:00', 10.90, 11.20, 10.85, 11.10, 1100000, 12210000.00),
('000002.SZ', '1d', '2024-01-01 09:30:00', 15.20, 15.50, 15.00, 15.35, 800000, 12280000.00),
('000002.SZ', '1d', '2024-01-02 09:30:00', 15.35, 15.80, 15.20, 15.65, 900000, 14085000.00),
('000002.SZ', '1d', '2024-01-03 09:30:00', 15.65, 16.00, 15.50, 15.85, 950000, 15057500.00),
('600000.SH', '1d', '2024-01-01 09:30:00', 8.50, 8.75, 8.40, 8.70, 2000000, 17400000.00),
('600000.SH', '1d', '2024-01-02 09:30:00', 8.70, 8.90, 8.60, 8.85, 1800000, 15930000.00),
('600000.SH', '1d', '2024-01-03 09:30:00', 8.85, 9.10, 8.80, 9.00, 1900000, 17100000.00)
ON DUPLICATE KEY UPDATE
    open = VALUES(open),
    high = VALUES(high),
    low = VALUES(low),
    close = VALUES(close),
    volume = VALUES(volume),
    amount = VALUES(amount),
    updated_at = CURRENT_TIMESTAMP;

-- 创建股票基本信息表（可选）
CREATE TABLE IF NOT EXISTS stock_info (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    stock_code VARCHAR(20) NOT NULL UNIQUE COMMENT '股票代码',
    stock_name VARCHAR(100) NOT NULL COMMENT '股票名称',
    market VARCHAR(20) NOT NULL COMMENT '市场，如 上海主板, 深圳主板',
    exchange VARCHAR(10) NOT NULL COMMENT '交易所，如 SH, SZ',
    industry VARCHAR(100) COMMENT '行业',
    sector VARCHAR(100) COMMENT '板块',
    list_date DATE COMMENT '上市日期',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_stock_code (stock_code),
    INDEX idx_market (market),
    INDEX idx_exchange (exchange)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='股票基本信息表';

-- 插入股票基本信息示例数据
INSERT INTO stock_info (stock_code, stock_name, market, exchange, industry, list_date) VALUES
('000001.SZ', '平安银行', '深圳主板', 'SZ', '银行', '1991-04-03'),
('000002.SZ', '万科A', '深圳主板', 'SZ', '房地产', '1991-01-29'),
('600000.SH', '浦发银行', '上海主板', 'SH', '银行', '1999-11-10')
ON DUPLICATE KEY UPDATE
    stock_name = VALUES(stock_name),
    market = VALUES(market),
    exchange = VALUES(exchange),
    industry = VALUES(industry),
    list_date = VALUES(list_date),
    updated_at = CURRENT_TIMESTAMP;
