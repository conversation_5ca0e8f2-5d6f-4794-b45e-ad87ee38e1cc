import cv2
import numpy as np
import pandas as pd
from dataclasses import dataclass
from typing import List, Tuple, Optional, Dict
import matplotlib.pyplot as plt
from matplotlib.patches import Rectangle
import matplotlib.patches as mpatches
from scipy import stats
from collections import defaultdict

@dataclass
class Candle:
    """K线数据结构"""
    x: int  # x坐标位置（中心）
    open: float  # 开盘价
    high: float  # 最高价
    low: float  # 最低价
    close: float  # 收盘价
    color: str  # 颜色类型：'red'(下跌), 'green'(上涨), 'yellow'(特殊), 'unknown'
    body_rect: Optional[Tuple[int, int, int, int]] = None  # 实体矩形 (x, y, width, height)
    upper_wick: Optional[Tuple[int, int, int, int]] = None  # 上影线 (x1, y1, x2, y2)
    lower_wick: Optional[Tuple[int, int, int, int]] = None  # 下影线 (x1, y1, x2, y2)
    confidence: float = 1.0  # 识别置信度
    
class EnhancedKLineRecognizer:
    """增强版K线图识别器"""
    
    def __init__(self, image_path: str):
        """
        初始化识别器
        
        Args:
            image_path: 图片路径
        """
        self.image_path = image_path
        self.image = cv2.imread(image_path)
        self.height, self.width = self.image.shape[:2]
        self.candles = []
        self.gray = cv2.cvtColor(self.image, cv2.COLOR_BGR2GRAY)
        self.edges = None
        self.interval = None  # K线间距
        
    def detect_candles(self) -> List[Candle]:
        """
        主识别流程
        
        Returns:
            K线列表
        """
        print("开始K线识别...")
        
        # 步骤1: 边缘检测，找到所有几何形状
        self._detect_edges()
        
        # 步骤2: 检测所有矩形（K线实体）
        rectangles = self._detect_rectangles()
        print(f"检测到 {len(rectangles)} 个矩形")
        
        # 步骤3: 检测所有垂直线（影线）
        vertical_lines = self._detect_vertical_lines()
        print(f"检测到 {len(vertical_lines)} 条垂直线")
        
        # 步骤4: 检测十字形状（组合K线实体和影线）
        crosses = self._detect_crosses(rectangles, vertical_lines)
        print(f"检测到 {len(crosses)} 个十字形状")
        
        # 步骤5: 将矩形和十字组合成K线
        self._assemble_candles(rectangles, crosses, vertical_lines)
        print(f"初步组装 {len(self.candles)} 根K线")
        
        # 步骤6: 计算K线间距
        self._calculate_interval()
        
        # 步骤7: 使用等间距先验知识填补遗漏的K线
        self._fill_missing_candles()
        
        # 步骤8: 识别颜色
        self._identify_colors()
        
        # 步骤9: 标准化价格数据
        self._normalize_prices()
        
        print(f"最终识别 {len(self.candles)} 根K线")
        
        return self.candles
    
    def _detect_edges(self):
        """边缘检测"""
        # 使用自适应阈值增强边缘
        blurred = cv2.GaussianBlur(self.gray, (3, 3), 0)
        
        # 多种边缘检测方法结合
        edges1 = cv2.Canny(blurred, 30, 100)
        edges2 = cv2.Canny(blurred, 50, 150)
        
        # 自适应阈值
        adaptive = cv2.adaptiveThreshold(blurred, 255, 
                                       cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                                       cv2.THRESH_BINARY_INV, 11, 2)
        
        # 组合边缘
        self.edges = cv2.bitwise_or(edges1, edges2)
        self.edges = cv2.bitwise_or(self.edges, adaptive)
        
        # 形态学操作增强连接
        kernel = np.ones((2, 2), np.uint8)
        self.edges = cv2.morphologyEx(self.edges, cv2.MORPH_CLOSE, kernel)
    
    def _detect_rectangles(self) -> List[Dict]:
        """
        检测所有矩形（K线实体）
        
        Returns:
            矩形列表
        """
        rectangles = []
        
        # 查找轮廓
        contours, _ = cv2.findContours(self.edges, cv2.RETR_EXTERNAL, 
                                      cv2.CHAIN_APPROX_SIMPLE)
        
        for contour in contours:
            # 轮廓近似
            epsilon = 0.02 * cv2.arcLength(contour, True)
            approx = cv2.approxPolyDP(contour, epsilon, True)
            
            # 检查是否为矩形（4个顶点）
            if len(approx) == 4:
                x, y, w, h = cv2.boundingRect(contour)
                
                # 过滤条件
                if w < 5 or h < 5:  # 太小
                    continue
                if w > 100 or h > 200:  # 太大
                    continue
                if w / h > 5 or h / w > 20:  # 比例不合理
                    continue
                
                # 计算矩形度（轮廓面积与边界框面积的比值）
                area = cv2.contourArea(contour)
                rect_area = w * h
                rectangularity = area / rect_area if rect_area > 0 else 0
                
                if rectangularity > 0.6:  # 足够接近矩形
                    rectangles.append({
                        'x': x,
                        'y': y,
                        'width': w,
                        'height': h,
                        'center_x': x + w // 2,
                        'center_y': y + h // 2,
                        'rectangularity': rectangularity
                    })
        
        # 额外检测：使用霍夫线变换检测矩形框
        rectangles.extend(self._detect_rectangles_by_lines())
        
        return rectangles
    
    def _detect_rectangles_by_lines(self) -> List[Dict]:
        """通过检测水平和垂直线的交点来找矩形"""
        rectangles = []
        
        # 检测所有线条
        lines = cv2.HoughLinesP(self.edges, 1, np.pi/180, 
                               threshold=20, minLineLength=10, maxLineGap=3)
        
        if lines is None:
            return rectangles
        
        # 分类为水平和垂直线
        h_lines = []  # 水平线
        v_lines = []  # 垂直线
        
        for line in lines:
            x1, y1, x2, y2 = line[0]
            angle = np.abs(np.arctan2(y2 - y1, x2 - x1))
            
            if angle < np.pi / 6:  # 接近水平（±30度）
                h_lines.append((x1, y1, x2, y2))
            elif angle > np.pi / 3:  # 接近垂直（60-90度）
                v_lines.append((x1, y1, x2, y2))
        
        # 查找可能组成矩形的线条组合
        # 这里使用简化的方法：查找垂直线对
        v_lines_by_x = defaultdict(list)
        for x1, y1, x2, y2 in v_lines:
            x_avg = (x1 + x2) // 2
            v_lines_by_x[x_avg].append((x1, y1, x2, y2))
        
        # 查找成对的垂直线
        x_positions = sorted(v_lines_by_x.keys())
        for i in range(len(x_positions) - 1):
            x1 = x_positions[i]
            for j in range(i + 1, min(i + 5, len(x_positions))):  # 查找附近的垂直线
                x2 = x_positions[j]
                width = x2 - x1
                
                if 10 < width < 80:  # 合理的K线宽度
                    # 检查是否有对应的水平线连接
                    for line1 in v_lines_by_x[x1]:
                        for line2 in v_lines_by_x[x2]:
                            y_min = max(min(line1[1], line1[3]), min(line2[1], line2[3]))
                            y_max = min(max(line1[1], line1[3]), max(line2[1], line2[3]))
                            
                            if y_max - y_min > 5:  # 有重叠区域
                                rectangles.append({
                                    'x': x1,
                                    'y': y_min,
                                    'width': width,
                                    'height': y_max - y_min,
                                    'center_x': (x1 + x2) // 2,
                                    'center_y': (y_min + y_max) // 2,
                                    'rectangularity': 0.7  # 估计值
                                })
        
        return rectangles
    
    def _detect_vertical_lines(self) -> List[Tuple[int, int, int, int]]:
        """检测所有垂直线（影线）"""
        vertical_lines = []
        
        # 使用霍夫线变换
        lines = cv2.HoughLinesP(self.edges, 1, np.pi/180, 
                               threshold=15, minLineLength=5, maxLineGap=2)
        
        if lines is not None:
            for line in lines:
                x1, y1, x2, y2 = line[0]
                
                # 检查是否为垂直线
                if abs(x1 - x2) <= 3:  # 允许小偏差
                    length = abs(y2 - y1)
                    if length >= 5:  # 最小长度要求
                        vertical_lines.append((x1, y1, x2, y2))
        
        return vertical_lines
    
    def _detect_crosses(self, rectangles: List[Dict], 
                       vertical_lines: List[Tuple]) -> List[Dict]:
        """
        检测十字形状（矩形+上下影线）
        
        Args:
            rectangles: 矩形列表
            vertical_lines: 垂直线列表
            
        Returns:
            十字形状列表
        """
        crosses = []
        
        for rect in rectangles:
            cross = {
                'rect': rect,
                'upper_wick': None,
                'lower_wick': None,
                'center_x': rect['center_x']
            }
            
            # 查找该矩形对应的影线
            for line in vertical_lines:
                x1, y1, x2, y2 = line
                line_x = (x1 + x2) // 2
                
                # 检查线是否在矩形的x范围内
                if abs(line_x - rect['center_x']) <= rect['width'] // 2 + 3:
                    line_top = min(y1, y2)
                    line_bottom = max(y1, y2)
                    
                    # 上影线
                    if line_top < rect['y'] and line_bottom >= rect['y'] - 5:
                        if cross['upper_wick'] is None or line_top < cross['upper_wick'][1]:
                            cross['upper_wick'] = (line_x, line_top, line_x, rect['y'])
                    
                    # 下影线
                    if line_bottom > rect['y'] + rect['height'] and line_top <= rect['y'] + rect['height'] + 5:
                        if cross['lower_wick'] is None or line_bottom > cross['lower_wick'][3]:
                            cross['lower_wick'] = (line_x, rect['y'] + rect['height'], 
                                                  line_x, line_bottom)
            
            # 如果有影线，添加到十字列表
            if cross['upper_wick'] or cross['lower_wick']:
                crosses.append(cross)
        
        return crosses
    
    def _assemble_candles(self, rectangles: List[Dict], 
                         crosses: List[Dict], 
                         vertical_lines: List[Tuple]):
        """组装K线"""
        used_rects = set()
        
        # 首先处理十字形状
        for cross in crosses:
            rect = cross['rect']
            used_rects.add(id(rect))
            
            candle = Candle(
                x=rect['center_x'],
                open=0,
                high=0,
                low=0,
                close=0,
                color='unknown',
                body_rect=(rect['x'], rect['y'], rect['width'], rect['height']),
                upper_wick=cross['upper_wick'],
                lower_wick=cross['lower_wick'],
                confidence=0.9
            )
            self.candles.append(candle)
        
        # 处理剩余的矩形（没有明显影线的K线）
        for rect in rectangles:
            if id(rect) not in used_rects:
                candle = Candle(
                    x=rect['center_x'],
                    open=0,
                    high=0,
                    low=0,
                    close=0,
                    color='unknown',
                    body_rect=(rect['x'], rect['y'], rect['width'], rect['height']),
                    confidence=0.7
                )
                self.candles.append(candle)
        
        # 处理独立的垂直线（可能是十字星）
        for line in vertical_lines:
            x1, y1, x2, y2 = line
            line_x = (x1 + x2) // 2
            
            # 检查是否已被使用
            is_used = False
            for candle in self.candles:
                if candle.body_rect and abs(line_x - candle.x) <= candle.body_rect[2] // 2 + 5:
                    is_used = True
                    break
            
            if not is_used and abs(y2 - y1) > 20:  # 足够长的独立线
                # 创建十字星K线
                mid_y = (y1 + y2) // 2
                candle = Candle(
                    x=line_x,
                    open=0,
                    high=0,
                    low=0,
                    close=0,
                    color='unknown',
                    body_rect=(line_x - 2, mid_y - 2, 4, 4),  # 小实体
                    upper_wick=(line_x, y1, line_x, mid_y),
                    lower_wick=(line_x, mid_y, line_x, y2),
                    confidence=0.5
                )
                self.candles.append(candle)
        
        # 按x坐标排序
        self.candles.sort(key=lambda c: c.x)
    
    def _calculate_interval(self):
        """计算K线间距"""
        if len(self.candles) < 2:
            return
        
        # 计算相邻K线的间距
        intervals = []
        for i in range(1, len(self.candles)):
            interval = self.candles[i].x - self.candles[i-1].x
            if 10 < interval < 200:  # 合理范围
                intervals.append(interval)
        
        if intervals:
            # 使用众数作为标准间距
            self.interval = int(stats.mode(intervals, keepdims=True)[0][0])
            print(f"检测到K线间距: {self.interval} 像素")
    
    def _fill_missing_candles(self):
        """使用等间距先验知识填补遗漏的K线"""
        if not self.interval or len(self.candles) < 2:
            return
        
        filled_candles = []
        
        for i in range(len(self.candles) - 1):
            filled_candles.append(self.candles[i])
            
            # 检查间距
            gap = self.candles[i+1].x - self.candles[i].x
            expected_count = round(gap / self.interval)
            
            if expected_count > 1:  # 有遗漏
                print(f"在位置 {self.candles[i].x} 和 {self.candles[i+1].x} 之间发现遗漏，尝试填补 {expected_count - 1} 根K线")
                
                # 在间隙中搜索可能遗漏的K线特征
                for j in range(1, expected_count):
                    expected_x = self.candles[i].x + j * self.interval
                    
                    # 在期望位置附近搜索特征
                    missing_candle = self._search_candle_at_position(expected_x)
                    if missing_candle:
                        filled_candles.append(missing_candle)
                    else:
                        print(f"  未能在位置 {expected_x} 找到K线特征")
        
        filled_candles.append(self.candles[-1])
        self.candles = filled_candles
        self.candles.sort(key=lambda c: c.x)
    
    def _search_candle_at_position(self, x: int, tolerance: int = 15) -> Optional[Candle]:
        """
        在指定位置搜索K线特征
        
        Args:
            x: 期望的x坐标
            tolerance: 搜索容差
            
        Returns:
            找到的K线或None
        """
        # 定义搜索区域
        roi_x = max(0, x - tolerance)
        roi_width = min(tolerance * 2, self.width - roi_x)
        roi = self.edges[:, roi_x:roi_x + roi_width]
        
        # 在ROI中查找垂直线
        lines = cv2.HoughLinesP(roi, 1, np.pi/180, 
                               threshold=10, minLineLength=5, maxLineGap=3)
        
        if lines is not None and len(lines) > 0:
            # 找到最长的垂直线
            best_line = None
            max_length = 0
            
            for line in lines:
                x1, y1, x2, y2 = line[0]
                if abs(x1 - x2) <= 2:  # 垂直线
                    length = abs(y2 - y1)
                    if length > max_length:
                        max_length = length
                        best_line = (x1 + roi_x, y1, x2 + roi_x, y2)
            
            if best_line and max_length > 10:
                # 创建一个简单的K线
                line_x = (best_line[0] + best_line[2]) // 2
                mid_y = (best_line[1] + best_line[3]) // 2
                
                return Candle(
                    x=line_x,
                    open=0,
                    high=0,
                    low=0,
                    close=0,
                    color='unknown',
                    body_rect=(line_x - 5, mid_y - 5, 10, 10),
                    upper_wick=(line_x, best_line[1], line_x, mid_y),
                    lower_wick=(line_x, mid_y, line_x, best_line[3]),
                    confidence=0.3
                )
        
        return None
    
    def _identify_colors(self):
        """识别K线颜色"""
        hsv = cv2.cvtColor(self.image, cv2.COLOR_BGR2HSV)
        
        for candle in self.candles:
            if not candle.body_rect:
                continue
            
            x, y, w, h = candle.body_rect
            
            # 提取K线实体区域
            roi = hsv[y:y+h, x:x+w]
            
            if roi.size == 0:
                continue
            
            # 计算主要颜色
            # 红色范围
            red_mask1 = cv2.inRange(roi, np.array([0, 50, 50]), np.array([10, 255, 255]))
            red_mask2 = cv2.inRange(roi, np.array([170, 50, 50]), np.array([180, 255, 255]))
            red_pixels = np.sum(red_mask1) + np.sum(red_mask2)
            
            # 绿色范围
            green_mask = cv2.inRange(roi, np.array([40, 50, 50]), np.array([80, 255, 255]))
            green_pixels = np.sum(green_mask)
            
            # 黄色范围
            yellow_mask = cv2.inRange(roi, np.array([20, 50, 50]), np.array([40, 255, 255]))
            yellow_pixels = np.sum(yellow_mask)
            
            # 判断主要颜色
            max_pixels = max(red_pixels, green_pixels, yellow_pixels)
            
            if max_pixels > 0:
                if red_pixels == max_pixels:
                    candle.color = 'red'
                elif green_pixels == max_pixels:
                    candle.color = 'green'
                elif yellow_pixels == max_pixels:
                    candle.color = 'yellow'
    
    def _normalize_prices(self):
        """标准化价格数据"""
        if not self.candles:
            return
        
        # 收集所有Y坐标
        all_y_values = []
        for candle in self.candles:
            if candle.body_rect:
                x, y, w, h = candle.body_rect
                all_y_values.extend([y, y + h])
            if candle.upper_wick:
                all_y_values.append(candle.upper_wick[1])
            if candle.lower_wick:
                all_y_values.append(candle.lower_wick[3])
        
        if not all_y_values:
            return
        
        min_y = min(all_y_values)
        max_y = max(all_y_values)
        y_range = max_y - min_y if max_y > min_y else 1
        
        # 标准化到0-100的价格范围
        for candle in self.candles:
            if candle.body_rect:
                x, y, w, h = candle.body_rect
                
                # 价格与Y坐标成反比
                top_price = 100 * (1 - (y - min_y) / y_range)
                bottom_price = 100 * (1 - (y + h - min_y) / y_range)
                
                # 根据颜色判断开盘收盘
                if candle.color == 'green':  # 上涨
                    candle.close = top_price
                    candle.open = bottom_price
                else:  # 下跌或未知
                    candle.open = top_price
                    candle.close = bottom_price
                
                # 最高价和最低价
                if candle.upper_wick:
                    candle.high = 100 * (1 - (candle.upper_wick[1] - min_y) / y_range)
                else:
                    candle.high = max(candle.open, candle.close)
                
                if candle.lower_wick:
                    candle.low = 100 * (1 - (candle.lower_wick[3] - min_y) / y_range)
                else:
                    candle.low = min(candle.open, candle.close)
    
    def export_to_dataframe(self) -> pd.DataFrame:
        """导出为DataFrame格式"""
        data = []
        for i, candle in enumerate(self.candles):
            data.append({
                'index': i,
                'x_position': candle.x,
                'open': round(candle.open, 2),
                'high': round(candle.high, 2),
                'low': round(candle.low, 2),
                'close': round(candle.close, 2),
                'color': candle.color,
                'change': round(candle.close - candle.open, 2),
                'change_pct': round((candle.close - candle.open) / candle.open * 100, 2) if candle.open != 0 else 0,
                'confidence': round(candle.confidence, 2)
            })
        
        return pd.DataFrame(data)
    
    def visualize_results(self):
        """可视化识别结果"""
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        
        # 1. 原始图片
        axes[0, 0].imshow(cv2.cvtColor(self.image, cv2.COLOR_BGR2RGB))
        axes[0, 0].set_title('Original K-Line Chart')
        axes[0, 0].axis('off')
        
        # 2. 边缘检测结果
        axes[0, 1].imshow(self.edges, cmap='gray')
        axes[0, 1].set_title('Edge Detection Result')
        axes[0, 1].axis('off')
        
        # 3. 识别标注
        annotated = self.image.copy()
        for candle in self.candles:
            if candle.body_rect:
                x, y, w, h = candle.body_rect
                color = (0, 255, 0) if candle.color == 'green' else \
                       (0, 0, 255) if candle.color == 'red' else \
                       (0, 255, 255) if candle.color == 'yellow' else (255, 255, 255)
                cv2.rectangle(annotated, (x, y), (x+w, y+h), color, 2)
                
                # 标注置信度
                cv2.putText(annotated, f"{candle.confidence:.1f}", 
                          (x, y-5), cv2.FONT_HERSHEY_SIMPLEX, 0.3, color, 1)
        
        axes[1, 0].imshow(cv2.cvtColor(annotated, cv2.COLOR_BGR2RGB))
        axes[1, 0].set_title('Detection Annotations')
        axes[1, 0].axis('off')
        
        # 4. 重建的K线图
        ax = axes[1, 1]
        ax.set_title('Reconstructed K-Line Data')
        ax.set_xlabel('Index')
        ax.set_ylabel('Price')
        ax.grid(True, alpha=0.3)
        
        for i, candle in enumerate(self.candles):
            # 设置颜色和透明度
            if candle.color == 'green':
                body_color = 'green'
                wick_color = 'green'
            elif candle.color == 'red':
                body_color = 'red'
                wick_color = 'red'
            elif candle.color == 'yellow':
                body_color = 'gold'
                wick_color = 'gold'
            else:
                body_color = 'gray'
                wick_color = 'gray'
            
            alpha = min(1.0, candle.confidence + 0.3)
            
            # 绘制影线
            ax.plot([i, i], [candle.low, candle.high], 
                   color=wick_color, linewidth=1, alpha=alpha)
            
            # 绘制实体
            body_height = abs(candle.close - candle.open)
            body_bottom = min(candle.open, candle.close)
            
            rect = Rectangle((i - 0.3, body_bottom), 0.6, body_height,
                           facecolor=body_color, edgecolor='black', 
                           linewidth=0.5, alpha=alpha)
            ax.add_patch(rect)
        
        ax.set_xlim(-0.5, len(self.candles) - 0.5)
        
        plt.tight_layout()
        plt.show()

def main():
    """主函数"""
    # 使用示例
    image_path = 'PixPin_2025-08-10_13-45-56.jpg'  # 替换为你的图片路径
    
    # 创建增强版识别器
    recognizer = EnhancedKLineRecognizer(image_path)
    
    # 执行识别
    candles = recognizer.detect_candles()
    
    # 导出数据
    df = recognizer.export_to_dataframe()
    
    print("\n" + "="*60)
    print("识别结果汇总：")
    print("="*60)
    print(f"识别K线总数: {len(candles)}")
    print(f"K线间距: {recognizer.interval} 像素")
    
    # 颜色统计
    color_stats = df['color'].value_counts()
    print("\n颜色分布：")
    for color, count in color_stats.items():
        print(f"  {color}: {count} 根 ({count/len(candles)*100:.1f}%)")
    
    # 置信度统计
    print(f"\n平均置信度: {df['confidence'].mean():.2f}")
    print(f"高置信度(>0.7): {len(df[df['confidence'] > 0.7])} 根")
    print(f"中置信度(0.5-0.7): {len(df[(df['confidence'] >= 0.5) & (df['confidence'] <= 0.7)])} 根")
    print(f"低置信度(<0.5): {len(df[df['confidence'] < 0.5])} 根")
    
    # 显示详细数据
    print("\n详细K线数据：")
    print(df.to_string())
    
    # 保存到CSV
    df.to_csv('enhanced_kline_data.csv', index=False)
    print("\n数据已保存到 enhanced_kline_data.csv")
    
    # 生成识别报告
    generate_report(recognizer, df)
    
    # 可视化结果
    recognizer.visualize_results()

def generate_report(recognizer, df):
    """生成详细的识别报告"""
    report = []
    report.append("\n" + "="*60)
    report.append("K线识别质量报告")
    report.append("="*60)
    
    # 检查等间距性
    if len(df) > 1:
        x_positions = df['x_position'].values
        intervals = np.diff(x_positions)
        interval_std = np.std(intervals)
        interval_mean = np.mean(intervals)
        
        report.append(f"\n间距分析：")
        report.append(f"  平均间距: {interval_mean:.1f} 像素")
        report.append(f"  间距标准差: {interval_std:.1f} 像素")
        report.append(f"  间距一致性: {'良好' if interval_std < interval_mean * 0.2 else '存在偏差'}")
        
        # 检测可能的遗漏
        large_gaps = []
        for i in range(len(intervals)):
            if intervals[i] > interval_mean * 1.5:
                large_gaps.append((i, i+1, intervals[i]))
        
        if large_gaps:
            report.append(f"\n检测到异常间距：")
            for start, end, gap in large_gaps:
                report.append(f"  K线 {start} 到 {end} 之间: {gap:.0f} 像素 (可能有遗漏)")
    
    # 数据完整性检查
    report.append(f"\n数据完整性：")
    complete_candles = df[(df['high'] >= df['low']) & 
                          (df['high'] >= df['open']) & 
                          (df['high'] >= df['close']) &
                          (df['low'] <= df['open']) & 
                          (df['low'] <= df['close'])]
    report.append(f"  完整K线: {len(complete_candles)}/{len(df)} ({len(complete_candles)/len(df)*100:.1f}%)")
    
    # 打印报告
    for line in report:
        print(line)
    
    # 保存报告
    with open('kline_recognition_report.txt', 'w', encoding='utf-8') as f:
        f.write('\n'.join(report))
    print("\n报告已保存到 kline_recognition_report.txt")

if __name__ == "__main__":
    main()