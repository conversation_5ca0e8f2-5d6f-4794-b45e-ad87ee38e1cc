import tkinter as tk
from tkinter import messagebox, scrolledtext, filedialog
import threading
import time
import pyautogui
from datetime import datetime
from PIL import Image, ImageTk
import os

class MouseCoordinateTracker:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("鼠标坐标显示器 - OpenCV截图助手")
        self.root.geometry("500x700")
        self.root.configure(bg='#f0f0f0')
        
        # 设置窗口图标和属性
        self.root.attributes('-topmost', True)
        self.root.resizable(True, True)
        
        # 状态变量
        self.is_tracking = True
        self.is_topmost = True
        self.coordinate_list = []
        
        # 图片相关变量
        self.current_image = None
        self.image_path = None
        self.image_coordinates = []
        self.canvas_scale = 1.0
        self.canvas_offset_x = 0
        self.canvas_offset_y = 0
        
        # 颜色主题
        self.colors = {
            'bg': '#f0f0f0',
            'primary': '#2196F3',
            'secondary': '#4CAF50',
            'accent': '#FF9800',
            'danger': '#F44336',
            'text': '#333333'
        }
        
        # 禁用pyautogui安全机制
        pyautogui.FAILSAFE = False
        
        self.create_widgets()
        self.start_coordinate_tracking()
        
    def create_widgets(self):
        """创建界面组件"""
        # 主标题
        title_frame = tk.Frame(self.root, bg=self.colors['bg'])
        title_frame.pack(fill=tk.X, padx=20, pady=(20, 10))
        
        title_label = tk.Label(
            title_frame, 
            text="🖱️ 鼠标坐标追踪器", 
            font=('微软雅黑', 18, 'bold'),
            bg=self.colors['bg'],
            fg=self.colors['primary']
        )
        title_label.pack()
        
        # 当前坐标显示区域
        self.create_coordinate_display()
        
        # 控制按钮区域
        self.create_control_buttons()
        
        # 图片处理区域
        self.create_image_section()
        
        # 坐标记录区域
        self.create_record_section()
        
        # OpenCV代码生成区域
        self.create_code_generation_section()
        
        # 状态栏
        self.create_status_bar()
        
        # 绑定键盘事件
        self.bind_keyboard_events()
    
    def create_coordinate_display(self):
        """创建坐标显示区域"""
        coord_frame = tk.LabelFrame(
            self.root, 
            text="实时坐标", 
            font=('微软雅黑', 12, 'bold'),
            bg=self.colors['bg'],
            fg=self.colors['text'],
            padx=10,
            pady=10
        )
        coord_frame.pack(fill=tk.X, padx=20, pady=10)
        
        # 坐标显示
        self.coord_var = tk.StringVar(value="X: 0, Y: 0")
        coord_label = tk.Label(
            coord_frame,
            textvariable=self.coord_var,
            font=('Consolas', 16, 'bold'),
            bg='white',
            fg=self.colors['primary'],
            relief=tk.SUNKEN,
            padx=20,
            pady=10
        )
        coord_label.pack(fill=tk.X)
        
        # 屏幕信息
        screen_width, screen_height = pyautogui.size()
        screen_info = tk.Label(
            coord_frame,
            text=f"屏幕分辨率: {screen_width} × {screen_height}",
            font=('微软雅黑', 10),
            bg=self.colors['bg'],
            fg='gray'
        )
        screen_info.pack(pady=(5, 0))
    
    def create_control_buttons(self):
        """创建控制按钮"""
        control_frame = tk.LabelFrame(
            self.root,
            text="控制选项",
            font=('微软雅黑', 12, 'bold'),
            bg=self.colors['bg'],
            fg=self.colors['text'],
            padx=10,
            pady=10
        )
        control_frame.pack(fill=tk.X, padx=20, pady=10)
        
        # 按钮容器
        button_container = tk.Frame(control_frame, bg=self.colors['bg'])
        button_container.pack(fill=tk.X)
        
        # 追踪控制按钮
        self.tracking_btn = tk.Button(
            button_container,
            text="⏸️ 停止追踪",
            command=self.toggle_tracking,
            font=('微软雅黑', 10, 'bold'),
            bg=self.colors['danger'],
            fg='white',
            relief=tk.RAISED,
            padx=20,
            pady=8
        )
        self.tracking_btn.pack(side=tk.LEFT, padx=(0, 10), fill=tk.X, expand=True)
        
        # 置顶控制按钮
        self.topmost_btn = tk.Button(
            button_container,
            text="📌 取消置顶",
            command=self.toggle_topmost,
            font=('微软雅黑', 10, 'bold'),
            bg=self.colors['accent'],
            fg='white',
            relief=tk.RAISED,
            padx=20,
            pady=8
        )
        self.topmost_btn.pack(side=tk.LEFT, fill=tk.X, expand=True)
    
    def create_image_section(self):
        """创建图片处理区域"""
        image_frame = tk.LabelFrame(
            self.root,
            text="图片坐标选择",
            font=('微软雅黑', 12, 'bold'),
            bg=self.colors['bg'],
            fg=self.colors['text'],
            padx=10,
            pady=10
        )
        image_frame.pack(fill=tk.X, padx=20, pady=10)
        
        # 图片控制按钮
        img_control = tk.Frame(image_frame, bg=self.colors['bg'])
        img_control.pack(fill=tk.X, pady=(0, 10))
        
        load_img_btn = tk.Button(
            img_control,
            text="📁 加载图片",
            command=self.load_image,
            font=('微软雅黑', 10, 'bold'),
            bg=self.colors['primary'],
            fg='white',
            relief=tk.RAISED,
            padx=15,
            pady=5
        )
        load_img_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        clear_img_btn = tk.Button(
            img_control,
            text="🗑️ 清空图片坐标",
            command=self.clear_image_coordinates,
            font=('微软雅黑', 10, 'bold'),
            bg=self.colors['danger'],
            fg='white',
            relief=tk.RAISED,
            padx=15,
            pady=5
        )
        clear_img_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # 图片信息显示
        self.image_info_var = tk.StringVar(value="未加载图片")
        info_label = tk.Label(
            img_control,
            textvariable=self.image_info_var,
            font=('微软雅黑', 9),
            bg=self.colors['bg'],
            fg='gray'
        )
        info_label.pack(side=tk.LEFT, padx=(20, 0))
        
        # 图片显示画布容器
        canvas_container = tk.Frame(image_frame, bg=self.colors['bg'])
        canvas_container.pack(fill=tk.BOTH, expand=True)
        
        # 创建可滚动的画布
        canvas_frame = tk.Frame(canvas_container, bg=self.colors['bg'])
        canvas_frame.pack(fill=tk.BOTH, expand=True)
        
        # 画布
        self.image_canvas = tk.Canvas(
            canvas_frame,
            bg='white',
            relief=tk.SUNKEN,
            bd=2,
            width=400,
            height=300,
            scrollregion=(0, 0, 400, 300)
        )
        
        # 滚动条
        h_scrollbar = tk.Scrollbar(canvas_frame, orient=tk.HORIZONTAL, command=self.image_canvas.xview)
        v_scrollbar = tk.Scrollbar(canvas_frame, orient=tk.VERTICAL, command=self.image_canvas.yview)
        
        self.image_canvas.configure(xscrollcommand=h_scrollbar.set, yscrollcommand=v_scrollbar.set)
        
        # 布局
        self.image_canvas.grid(row=0, column=0, sticky="nsew")
        h_scrollbar.grid(row=1, column=0, sticky="ew")
        v_scrollbar.grid(row=0, column=1, sticky="ns")
        
        canvas_frame.grid_rowconfigure(0, weight=1)
        canvas_frame.grid_columnconfigure(0, weight=1)
        
        # 绑定画布点击事件
        self.image_canvas.bind('<Button-1>', self.on_image_click)
        self.image_canvas.bind('<MouseWheel>', self.on_canvas_scroll)
        
        # 图片坐标显示
        coord_display = tk.Frame(image_frame, bg=self.colors['bg'])
        coord_display.pack(fill=tk.X, pady=(10, 0))
        
        tk.Label(
            coord_display,
            text="图片坐标点:",
            font=('微软雅黑', 10, 'bold'),
            bg=self.colors['bg']
        ).pack(side=tk.LEFT)
        
        self.image_coords_var = tk.StringVar(value="未选择")
        tk.Label(
            coord_display,
            textvariable=self.image_coords_var,
            font=('Consolas', 10),
            bg=self.colors['bg'],
            fg=self.colors['primary']
        ).pack(side=tk.LEFT, padx=(10, 0))
    
    def create_record_section(self):
        """创建坐标记录区域"""
        record_frame = tk.LabelFrame(
            self.root,
            text="坐标记录",
            font=('微软雅黑', 12, 'bold'),
            bg=self.colors['bg'],
            fg=self.colors['text'],
            padx=10,
            pady=10
        )
        record_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
        
        # 记录控制按钮
        record_control = tk.Frame(record_frame, bg=self.colors['bg'])
        record_control.pack(fill=tk.X, pady=(0, 10))
        
        record_btn = tk.Button(
            record_control,
            text="📍 记录当前坐标",
            command=self.record_coordinate,
            font=('微软雅黑', 10, 'bold'),
            bg=self.colors['secondary'],
            fg='white',
            relief=tk.RAISED,
            padx=15,
            pady=5
        )
        record_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        clear_btn = tk.Button(
            record_control,
            text="🗑️ 清空记录",
            command=self.clear_records,
            font=('微软雅黑', 10, 'bold'),
            bg=self.colors['danger'],
            fg='white',
            relief=tk.RAISED,
            padx=15,
            pady=5
        )
        clear_btn.pack(side=tk.LEFT)
        
        # 坐标列表显示
        list_label = tk.Label(
            record_frame,
            text="已记录的坐标点:",
            font=('微软雅黑', 10, 'bold'),
            bg=self.colors['bg'],
            fg=self.colors['text']
        )
        list_label.pack(anchor=tk.W, pady=(0, 5))
        
        # 使用Listbox显示坐标
        listbox_frame = tk.Frame(record_frame, bg=self.colors['bg'])
        listbox_frame.pack(fill=tk.BOTH, expand=True)
        
        self.coord_listbox = tk.Listbox(
            listbox_frame,
            font=('Consolas', 10),
            bg='white',
            selectbackground=self.colors['primary'],
            height=8
        )
        self.coord_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # 滚动条
        scrollbar = tk.Scrollbar(listbox_frame, orient=tk.VERTICAL)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        self.coord_listbox.config(yscrollcommand=scrollbar.set)
        scrollbar.config(command=self.coord_listbox.yview)
        
        # 双击删除提示
        tip_label = tk.Label(
            record_frame,
            text="💡 提示: 双击列表项可删除单个坐标",
            font=('微软雅黑', 8),
            bg=self.colors['bg'],
            fg='gray'
        )
        tip_label.pack(anchor=tk.W, pady=(5, 0))
        
        # 绑定双击事件
        self.coord_listbox.bind('<Double-Button-1>', self.delete_selected_coordinate)
    
    def create_code_generation_section(self):
        """创建代码生成区域"""
        code_frame = tk.LabelFrame(
            self.root,
            text="OpenCV代码生成",
            font=('微软雅黑', 12, 'bold'),
            bg=self.colors['bg'],
            fg=self.colors['text'],
            padx=10,
            pady=10
        )
        code_frame.pack(fill=tk.X, padx=20, pady=10)
        
        generate_btn = tk.Button(
            code_frame,
            text="🔧 生成截图代码",
            command=self.generate_opencv_code,
            font=('微软雅黑', 11, 'bold'),
            bg=self.colors['primary'],
            fg='white',
            relief=tk.RAISED,
            padx=30,
            pady=10
        )
        generate_btn.pack()
    
    def create_status_bar(self):
        """创建状态栏"""
        status_frame = tk.Frame(self.root, bg='#e0e0e0', relief=tk.SUNKEN, bd=1)
        status_frame.pack(fill=tk.X, side=tk.BOTTOM)
        
        self.status_var = tk.StringVar(value="状态: 正在追踪鼠标坐标...")
        status_label = tk.Label(
            status_frame,
            textvariable=self.status_var,
            font=('微软雅黑', 9),
            bg='#e0e0e0',
            fg=self.colors['text'],
            anchor=tk.W
        )
        status_label.pack(side=tk.LEFT, padx=10, pady=2)
        
        # 快捷键提示
        shortcut_label = tk.Label(
            status_frame,
            text="快捷键: [空格]记录 [C]清空 [T]追踪 [P]置顶 [L]加载图片",
            font=('微软雅黑', 8),
            bg='#e0e0e0',
            fg='gray',
            anchor=tk.E
        )
        shortcut_label.pack(side=tk.RIGHT, padx=10, pady=2)
    
    def bind_keyboard_events(self):
        """绑定键盘事件"""
        self.root.bind('<KeyPress-space>', lambda e: self.record_coordinate())
        self.root.bind('<KeyPress-c>', lambda e: self.clear_records())
        self.root.bind('<KeyPress-t>', lambda e: self.toggle_tracking())
        self.root.bind('<KeyPress-p>', lambda e: self.toggle_topmost())
        self.root.bind('<KeyPress-l>', lambda e: self.load_image())
        
        # 确保窗口可以接收键盘事件
        self.root.focus_set()
    
    def start_coordinate_tracking(self):
        """启动坐标追踪线程"""
        def track_mouse():
            while True:
                if self.is_tracking:
                    try:
                        x, y = pyautogui.position()
                        self.coord_var.set(f"X: {x}, Y: {y}")
                    except Exception as e:
                        print(f"追踪错误: {e}")
                time.sleep(0.05)  # 提高刷新率
        
        tracking_thread = threading.Thread(target=track_mouse, daemon=True)
        tracking_thread.start()
    
    def toggle_tracking(self):
        """切换追踪状态"""
        self.is_tracking = not self.is_tracking
        
        if self.is_tracking:
            self.tracking_btn.config(
                text="⏸️ 停止追踪", 
                bg=self.colors['danger']
            )
            self.status_var.set("状态: 正在追踪鼠标坐标...")
        else:
            self.tracking_btn.config(
                text="▶️ 开始追踪", 
                bg=self.colors['secondary']
            )
            self.status_var.set("状态: 已停止追踪")
    
    def toggle_topmost(self):
        """切换窗口置顶状态"""
        self.is_topmost = not self.is_topmost
        self.root.attributes('-topmost', self.is_topmost)
        
        if self.is_topmost:
            self.topmost_btn.config(text="📌 取消置顶")
        else:
            self.topmost_btn.config(text="📌 设为置顶")
    
    def record_coordinate(self):
        """记录当前坐标"""
        try:
            x, y = pyautogui.position()
            timestamp = datetime.now().strftime("%H:%M:%S")
            
            coord_data = {
                'x': x,
                'y': y,
                'time': timestamp,
                'index': len(self.coordinate_list) + 1
            }
            
            self.coordinate_list.append(coord_data)
            
            # 添加到列表框
            display_text = f"{coord_data['index']:2d}. ({x:4d}, {y:4d}) - {timestamp}"
            self.coord_listbox.insert(tk.END, display_text)
            
            # 滚动到最新项
            self.coord_listbox.see(tk.END)
            
            # 更新状态
            count = len(self.coordinate_list)
            self.status_var.set(f"状态: 已记录坐标 ({x}, {y}) - 共 {count} 个点")
            
        except Exception as e:
            messagebox.showerror("错误", f"记录坐标失败: {e}")
    
    def delete_selected_coordinate(self, event):
        """删除选中的坐标"""
        selection = self.coord_listbox.curselection()
        if selection:
            index = selection[0]
            self.coord_listbox.delete(index)
            del self.coordinate_list[index]
            
            # 重新编号
            self.refresh_coordinate_list()
    
    def refresh_coordinate_list(self):
        """刷新坐标列表显示"""
        self.coord_listbox.delete(0, tk.END)
        
        for i, coord in enumerate(self.coordinate_list):
            coord['index'] = i + 1
            display_text = f"{coord['index']:2d}. ({coord['x']:4d}, {coord['y']:4d}) - {coord['time']}"
            self.coord_listbox.insert(tk.END, display_text)
    
    def clear_records(self):
        """清空所有记录"""
        if self.coordinate_list:
            result = messagebox.askyesno("确认", "确定要清空所有坐标记录吗？")
            if result:
                self.coordinate_list.clear()
                self.coord_listbox.delete(0, tk.END)
                self.status_var.set("状态: 已清空所有记录")
    
    def load_image(self):
        """加载图片"""
        file_types = [
            ("图片文件", "*.jpg;*.jpeg;*.png;*.bmp;*.gif;*.tiff"),
            ("JPEG文件", "*.jpg;*.jpeg"),
            ("PNG文件", "*.png"),
            ("所有文件", "*.*")
        ]
        
        file_path = filedialog.askopenfilename(
            title="选择图片文件",
            filetypes=file_types
        )
        
        if file_path:
            try:
                # 加载图片
                self.image_path = file_path
                self.current_image = Image.open(file_path)
                
                # 获取图片信息
                width, height = self.current_image.size
                file_size = os.path.getsize(file_path) / 1024  # KB
                
                # 更新信息显示
                filename = os.path.basename(file_path)
                self.image_info_var.set(f"{filename} ({width}×{height}, {file_size:.1f}KB)")
                
                # 显示图片
                self.display_image()
                
                # 清空之前的图片坐标
                self.image_coordinates.clear()
                self.update_image_coordinates_display()
                
                self.status_var.set(f"状态: 已加载图片 {filename}")
                
            except Exception as e:
                messagebox.showerror("错误", f"加载图片失败: {e}")
    
    def display_image(self):
        """在画布上显示图片"""
        if not self.current_image:
            return
        
        # 清空画布
        self.image_canvas.delete("all")
        
        # 计算显示尺寸
        canvas_width = 400
        canvas_height = 300
        img_width, img_height = self.current_image.size
        
        # 计算缩放比例
        scale_x = canvas_width / img_width
        scale_y = canvas_height / img_height
        self.canvas_scale = min(scale_x, scale_y, 1.0)  # 不放大，只缩小
        
        # 缩放图片
        display_width = int(img_width * self.canvas_scale)
        display_height = int(img_height * self.canvas_scale)
        
        display_image = self.current_image.resize((display_width, display_height), Image.Resampling.LANCZOS)
        self.photo = ImageTk.PhotoImage(display_image)
        
        # 计算居中位置
        self.canvas_offset_x = (canvas_width - display_width) // 2
        self.canvas_offset_y = (canvas_height - display_height) // 2
        
        # 显示图片
        self.image_canvas.create_image(
            self.canvas_offset_x, self.canvas_offset_y, 
            anchor=tk.NW, image=self.photo, tags="image"
        )
        
        # 更新滚动区域
        self.image_canvas.configure(scrollregion=self.image_canvas.bbox("all"))
        
        # 重绘已有的坐标点
        self.redraw_image_coordinates()
    
    def on_image_click(self, event):
        """处理图片点击事件"""
        if not self.current_image:
            return
        
        # 获取点击位置
        canvas_x = self.image_canvas.canvasx(event.x)
        canvas_y = self.image_canvas.canvasy(event.y)
        
        # 转换为图片坐标
        img_x = int((canvas_x - self.canvas_offset_x) / self.canvas_scale)
        img_y = int((canvas_y - self.canvas_offset_y) / self.canvas_scale)
        
        # 检查点击是否在图片范围内
        img_width, img_height = self.current_image.size
        if 0 <= img_x < img_width and 0 <= img_y < img_height:
            # 记录坐标
            timestamp = datetime.now().strftime("%H:%M:%S")
            coord_data = {
                'x': img_x,
                'y': img_y,
                'time': timestamp,
                'canvas_x': canvas_x,
                'canvas_y': canvas_y
            }
            
            self.image_coordinates.append(coord_data)
            
            # 在画布上绘制标记
            self.draw_coordinate_marker(canvas_x, canvas_y, len(self.image_coordinates))
            
            # 更新显示
            self.update_image_coordinates_display()
            
            self.status_var.set(f"状态: 在图片上记录坐标 ({img_x}, {img_y})")
    
    def draw_coordinate_marker(self, canvas_x, canvas_y, index):
        """在画布上绘制坐标标记"""
        colors = ['red', 'green', 'blue', 'orange', 'purple', 'brown']
        color = colors[(index - 1) % len(colors)]
        
        # 绘制圆形标记
        r = 5
        self.image_canvas.create_oval(
            canvas_x - r, canvas_y - r, canvas_x + r, canvas_y + r,
            fill=color, outline='white', width=2, tags="marker"
        )
        
        # 绘制编号
        self.image_canvas.create_text(
            canvas_x + 15, canvas_y - 15,
            text=str(index), fill=color, font=('Arial', 10, 'bold'),
            tags="marker"
        )
    
    def redraw_image_coordinates(self):
        """重新绘制所有坐标标记"""
        # 清除旧标记
        self.image_canvas.delete("marker")
        
        # 重绘所有标记
        for i, coord in enumerate(self.image_coordinates):
            # 重新计算画布坐标
            canvas_x = coord['x'] * self.canvas_scale + self.canvas_offset_x
            canvas_y = coord['y'] * self.canvas_scale + self.canvas_offset_y
            
            # 更新画布坐标
            coord['canvas_x'] = canvas_x
            coord['canvas_y'] = canvas_y
            
            # 绘制标记
            self.draw_coordinate_marker(canvas_x, canvas_y, i + 1)
    
    def update_image_coordinates_display(self):
        """更新图片坐标显示"""
        if not self.image_coordinates:
            self.image_coords_var.set("未选择")
        else:
            count = len(self.image_coordinates)
            last_coord = self.image_coordinates[-1]
            self.image_coords_var.set(
                f"共{count}个点，最新: ({last_coord['x']}, {last_coord['y']})"
            )
    
    def clear_image_coordinates(self):
        """清空图片坐标"""
        if self.image_coordinates:
            result = messagebox.askyesno("确认", "确定要清空图片上的所有坐标点吗？")
            if result:
                self.image_coordinates.clear()
                self.image_canvas.delete("marker")
                self.update_image_coordinates_display()
                self.status_var.set("状态: 已清空图片坐标")
    
    def on_canvas_scroll(self, event):
        """处理画布滚轮事件"""
        # 这里可以添加缩放功能
        pass
    
    def generate_opencv_code(self):
        """生成OpenCV代码"""
        if not self.coordinate_list:
            messagebox.showwarning("警告", "请先记录至少一个坐标点！")
            return
        
        # 创建代码显示窗口
        self.show_code_window()
    
    def show_code_window(self):
        """显示代码窗口"""
        code_window = tk.Toplevel(self.root)
        code_window.title("生成的OpenCV代码")
        code_window.geometry("800x600")
        code_window.configure(bg=self.colors['bg'])
        
        # 代码类型选择
        type_frame = tk.Frame(code_window, bg=self.colors['bg'])
        type_frame.pack(fill=tk.X, padx=20, pady=10)
        
        type_label = tk.Label(
            type_frame,
            text="代码类型:",
            font=('微软雅黑', 12, 'bold'),
            bg=self.colors['bg']
        )
        type_label.pack(side=tk.LEFT)
        
        # 根据坐标数量自动选择合适的代码类型
        screen_coord_count = len(self.coordinate_list)
        image_coord_count = len(self.image_coordinates)
        
        if image_coord_count > 0:
            if image_coord_count == 1:
                default_type = "image_single"
            elif image_coord_count == 2:
                default_type = "image_rectangle"
            else:
                default_type = "image_multiple"
        elif screen_coord_count == 1:
            default_type = "single"
        elif screen_coord_count == 2:
            default_type = "rectangle"
        else:
            default_type = "multiple"
        
        self.code_type = tk.StringVar(value=default_type)
        
        type_options = [
            ("屏幕单点标记", "single"),
            ("屏幕矩形截图", "rectangle"),
            ("屏幕多点标记", "multiple"),
            ("图片单点标记", "image_single"),
            ("图片矩形截图", "image_rectangle"),
            ("图片多点标记", "image_multiple")
        ]
        
        for text, value in type_options:
            rb = tk.Radiobutton(
                type_frame,
                text=text,
                variable=self.code_type,
                value=value,
                command=lambda: self.update_code_display(code_text),
                font=('微软雅黑', 10),
                bg=self.colors['bg']
            )
            rb.pack(side=tk.LEFT, padx=20)
        
        # 代码显示区域
        code_text = scrolledtext.ScrolledText(
            code_window,
            font=('Consolas', 10),
            bg='#2d2d2d',
            fg='#f8f8f2',
            insertbackground='white',
            wrap=tk.WORD
        )
        code_text.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
        
        # 按钮区域
        button_frame = tk.Frame(code_window, bg=self.colors['bg'])
        button_frame.pack(fill=tk.X, padx=20, pady=10)
        
        copy_btn = tk.Button(
            button_frame,
            text="📋 复制代码",
            command=lambda: self.copy_code_to_clipboard(code_text.get(1.0, tk.END)),
            font=('微软雅黑', 10, 'bold'),
            bg=self.colors['secondary'],
            fg='white',
            padx=20,
            pady=5
        )
        copy_btn.pack(side=tk.LEFT)
        
        save_btn = tk.Button(
            button_frame,
            text="💾 保存代码",
            command=lambda: self.save_code_to_file(code_text.get(1.0, tk.END)),
            font=('微软雅黑', 10, 'bold'),
            bg=self.colors['primary'],
            fg='white',
            padx=20,
            pady=5
        )
        save_btn.pack(side=tk.LEFT, padx=10)
        
        # 初始化代码显示
        self.update_code_display(code_text)
    
    def update_code_display(self, code_text):
        """更新代码显示"""
        code_text.delete(1.0, tk.END)
        generated_code = self.create_opencv_code()
        code_text.insert(1.0, generated_code)
    
    def create_opencv_code(self):
        """生成OpenCV代码"""
        code_type = self.code_type.get()
        screen_coords = self.coordinate_list
        image_coords = self.image_coordinates
        
        if code_type == "single" and screen_coords:
            return self.generate_single_point_code(screen_coords[0])
        elif code_type == "rectangle" and len(screen_coords) >= 2:
            return self.generate_rectangle_code(screen_coords[:2])
        elif code_type == "multiple" and screen_coords:
            return self.generate_multiple_points_code(screen_coords)
        elif code_type == "image_single" and image_coords:
            return self.generate_image_single_point_code(image_coords[0])
        elif code_type == "image_rectangle" and len(image_coords) >= 2:
            return self.generate_image_rectangle_code(image_coords[:2])
        elif code_type == "image_multiple" and image_coords:
            return self.generate_image_multiple_points_code(image_coords)
        else:
            return "# 请先记录相应数量的坐标点"
    
    def generate_single_point_code(self, coord):
        """生成单点标记代码"""
        x, y = coord['x'], coord['y']
        return f'''# 单点坐标标记代码
# 记录时间: {coord['time']}
import cv2
import numpy as np
from PIL import ImageGrab

# 目标坐标点
target_point = ({x}, {y})

# 截取屏幕
screenshot = ImageGrab.grab()
screenshot = np.array(screenshot)
screenshot = cv2.cvtColor(screenshot, cv2.COLOR_RGB2BGR)

# 在目标点绘制标记
cv2.circle(screenshot, target_point, 8, (0, 0, 255), -1)  # 红色实心圆
cv2.circle(screenshot, target_point, 15, (0, 255, 0), 2)  # 绿色圆环
cv2.putText(screenshot, f'({x}, {y})', (x + 20, y - 20), 
           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)

# 显示结果
cv2.imshow('Point Marker', screenshot)
cv2.waitKey(0)
cv2.destroyAllWindows()

# 保存截图
# cv2.imwrite('point_marker.png', screenshot)
'''
    
    def generate_rectangle_code(self, coords):
        """生成矩形截图代码"""
        x1, y1 = coords[0]['x'], coords[0]['y']
        x2, y2 = coords[1]['x'], coords[1]['y']
        
        left = min(x1, x2)
        top = min(y1, y2)
        right = max(x1, x2)
        bottom = max(y1, y2)
        width = right - left
        height = bottom - top
        
        return f'''# 矩形区域截图代码
# 点1记录时间: {coords[0]['time']}
# 点2记录时间: {coords[1]['time']}
import cv2
import numpy as np
from PIL import ImageGrab

# 原始坐标点
point1 = ({x1}, {y1})
point2 = ({x2}, {y2})

# 计算矩形边界
left, top = {left}, {top}
right, bottom = {right}, {bottom}
width, height = {width}, {height}

print(f"截图区域: 左上角({left}, {top}), 右下角({right}, {bottom})")
print(f"区域大小: {width} × {height} 像素")

# 方法1: 截取指定区域
cropped_screenshot = ImageGrab.grab(bbox=(left, top, right, bottom))
cropped_array = np.array(cropped_screenshot)
cropped_array = cv2.cvtColor(cropped_array, cv2.COLOR_RGB2BGR)

# 显示截取的区域
cv2.imshow('Cropped Region', cropped_array)

# 方法2: 全屏截图并标记区域
full_screenshot = ImageGrab.grab()
full_array = np.array(full_screenshot)
full_array = cv2.cvtColor(full_array, cv2.COLOR_RGB2BGR)

# 绘制矩形框
cv2.rectangle(full_array, (left, top), (right, bottom), (0, 255, 0), 3)
cv2.putText(full_array, f'{width}x{height}', (left, top-10), 
           cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 0), 2)

# 显示带标记的全屏截图
cv2.imshow('Full Screenshot with Marker', full_array)

cv2.waitKey(0)
cv2.destroyAllWindows()

# 保存截图
# cv2.imwrite('cropped_region.png', cropped_array)
# cv2.imwrite('marked_screenshot.png', full_array)
'''
    
    def generate_multiple_points_code(self, coords):
        """生成多点标记代码"""
        coords_list = [(coord['x'], coord['y']) for coord in coords]
        
        return f'''# 多点坐标标记代码
# 共记录 {len(coords)} 个坐标点
import cv2
import numpy as np
from PIL import ImageGrab

# 所有坐标点
coordinates = {coords_list}

# 截取屏幕
screenshot = ImageGrab.grab()
screenshot = np.array(screenshot)
screenshot = cv2.cvtColor(screenshot, cv2.COLOR_RGB2BGR)

# 定义颜色列表 (BGR格式)
colors = [
    (0, 0, 255),    # 红色
    (0, 255, 0),    # 绿色
    (255, 0, 0),    # 蓝色
    (0, 255, 255),  # 黄色
    (255, 0, 255),  # 紫色
    (255, 255, 0),  # 青色
]

# 标记每个坐标点
for i, (x, y) in enumerate(coordinates):
    color = colors[i % len(colors)]
    
    # 绘制标记点
    cv2.circle(screenshot, (x, y), 8, color, -1)
    cv2.circle(screenshot, (x, y), 15, (255, 255, 255), 2)
    
    # 添加标号
    cv2.putText(screenshot, f'{i+1}', (x-8, y+5), 
               cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
    
    # 添加坐标信息
    cv2.putText(screenshot, f'({x},{y})', (x+20, y-20), 
               cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2)

# 连接所有点形成路径
if len(coordinates) > 1:
    pts = np.array(coordinates, np.int32)
    cv2.polylines(screenshot, [pts], False, (255, 255, 255), 2)

# 计算并绘制边界框
if len(coordinates) >= 2:
    x_coords = [pt[0] for pt in coordinates]
    y_coords = [pt[1] for pt in coordinates]
    bbox_left, bbox_right = min(x_coords), max(x_coords)
    bbox_top, bbox_bottom = min(y_coords), max(y_coords)
    
    cv2.rectangle(screenshot, (bbox_left, bbox_top), 
                 (bbox_right, bbox_bottom), (0, 255, 255), 2)

# 显示结果
cv2.imshow('Multiple Points Marker', screenshot)
cv2.waitKey(0)
cv2.destroyAllWindows()

# 如需截取边界框区域
if len(coordinates) >= 2:
    bbox_cropped = ImageGrab.grab(bbox=(bbox_left, bbox_top, bbox_right, bbox_bottom))
    bbox_array = np.array(bbox_cropped)
    bbox_array = cv2.cvtColor(bbox_array, cv2.COLOR_RGB2BGR)
    
    cv2.imshow('Bounding Box Region', bbox_array)
    cv2.waitKey(0)
    cv2.destroyAllWindows()

# 保存截图
# cv2.imwrite('multiple_points.png', screenshot)
'''
    
    def generate_image_single_point_code(self, coord):
        """生成图片单点标记代码"""
        x, y = coord['x'], coord['y']
        return f'''# 图片单点坐标标记代码
# 图片路径: {self.image_path}
# 记录时间: {coord['time']}
import cv2
import numpy as np

# 加载图片
image_path = r"{self.image_path}"
image = cv2.imread(image_path)

if image is None:
    print(f"无法加载图片: {{image_path}}")
    exit()

# 目标坐标点
target_point = ({x}, {y})

# 在目标点绘制标记
cv2.circle(image, target_point, 8, (0, 0, 255), -1)  # 红色实心圆
cv2.circle(image, target_point, 15, (0, 255, 0), 2)  # 绿色圆环
cv2.putText(image, f'({x}, {y})', (x + 20, y - 20), 
           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)

# 显示结果
cv2.imshow('Image Point Marker', image)
cv2.waitKey(0)
cv2.destroyAllWindows()

# 保存标记后的图片
# cv2.imwrite('marked_image.png', image)
'''
    
    def generate_image_rectangle_code(self, coords):
        """生成图片矩形区域代码"""
        x1, y1 = coords[0]['x'], coords[0]['y']
        x2, y2 = coords[1]['x'], coords[1]['y']
        
        left = min(x1, x2)
        top = min(y1, y2)
        right = max(x1, x2)
        bottom = max(y1, y2)
        width = right - left
        height = bottom - top
        
        return f'''# 图片矩形区域处理代码
# 图片路径: {self.image_path}
# 点1记录时间: {coords[0]['time']}
# 点2记录时间: {coords[1]['time']}
import cv2
import numpy as np

# 加载图片
image_path = r"{self.image_path}"
original_image = cv2.imread(image_path)

if original_image is None:
    print(f"无法加载图片: {{image_path}}")
    exit()

# 原始坐标点
point1 = ({x1}, {y1})
point2 = ({x2}, {y2})

# 计算矩形边界
left, top = {left}, {top}
right, bottom = {right}, {bottom}
width, height = {width}, {height}

print(f"截取区域: 左上角({left}, {top}), 右下角({right}, {bottom})")
print(f"区域大小: {width} × {height} 像素")

# 方法1: 截取指定区域
cropped_region = original_image[top:bottom, left:right]

# 显示截取的区域
cv2.imshow('Cropped Region', cropped_region)

# 方法2: 在原图上标记区域
marked_image = original_image.copy()
cv2.rectangle(marked_image, (left, top), (right, bottom), (0, 255, 0), 3)
cv2.putText(marked_image, f'{width}x{height}', (left, top-10), 
           cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 0), 2)

# 显示带标记的原图
cv2.imshow('Original with Marker', marked_image)

cv2.waitKey(0)
cv2.destroyAllWindows()

# 保存结果
# cv2.imwrite('cropped_region.png', cropped_region)
# cv2.imwrite('marked_image.png', marked_image)
'''
    
    def generate_image_multiple_points_code(self, coords):
        """生成图片多点标记代码"""
        coords_list = [(coord['x'], coord['y']) for coord in coords]
        
        return f'''# 图片多点坐标标记代码
# 图片路径: {self.image_path}
# 共记录 {len(coords)} 个坐标点
import cv2
import numpy as np

# 加载图片
image_path = r"{self.image_path}"
image = cv2.imread(image_path)

if image is None:
    print(f"无法加载图片: {{image_path}}")
    exit()

# 所有坐标点
coordinates = {coords_list}

# 定义颜色列表 (BGR格式)
colors = [
    (0, 0, 255),    # 红色
    (0, 255, 0),    # 绿色
    (255, 0, 0),    # 蓝色
    (0, 255, 255),  # 黄色
    (255, 0, 255),  # 紫色
    (255, 255, 0),  # 青色
]

# 标记每个坐标点
for i, (x, y) in enumerate(coordinates):
    color = colors[i % len(colors)]
    
    # 绘制标记点
    cv2.circle(image, (x, y), 8, color, -1)
    cv2.circle(image, (x, y), 15, (255, 255, 255), 2)
    
    # 添加标号
    cv2.putText(image, f'{i+1}', (x-8, y+5), 
               cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
    
    # 添加坐标信息
    cv2.putText(image, f'({x},{y})', (x+20, y-20), 
               cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2)

# 连接所有点形成路径
if len(coordinates) > 1:
    pts = np.array(coordinates, np.int32)
    cv2.polylines(image, [pts], False, (255, 255, 255), 2)

# 计算并绘制边界框
if len(coordinates) >= 2:
    x_coords = [pt[0] for pt in coordinates]
    y_coords = [pt[1] for pt in coordinates]
    bbox_left, bbox_right = min(x_coords), max(x_coords)
    bbox_top, bbox_bottom = min(y_coords), max(y_coords)
    
    cv2.rectangle(image, (bbox_left, bbox_top), 
                 (bbox_right, bbox_bottom), (0, 255, 255), 2)

# 显示结果
cv2.imshow('Image Multiple Points Marker', image)
cv2.waitKey(0)
cv2.destroyAllWindows()

# 如需截取边界框区域
if len(coordinates) >= 2:
    bbox_cropped = image[bbox_top:bbox_bottom, bbox_left:bbox_right]
    cv2.imshow('Bounding Box Region', bbox_cropped)
    cv2.waitKey(0)
    cv2.destroyAllWindows()

# 保存标记后的图片
# cv2.imwrite('image_multiple_points.png', image)
'''
    
    def copy_code_to_clipboard(self, code):
        """复制代码到剪贴板"""
        self.root.clipboard_clear()
        self.root.clipboard_append(code)
        self.root.update()
        messagebox.showinfo("成功", "代码已复制到剪贴板！")
    
    def save_code_to_file(self, code):
        """保存代码到文件"""
        from tkinter import filedialog
        
        filename = filedialog.asksaveasfilename(
            defaultextension=".py",
            filetypes=[("Python文件", "*.py"), ("所有文件", "*.*")],
            title="保存OpenCV代码"
        )
        
        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(code)
                messagebox.showinfo("成功", f"代码已保存到: {filename}")
            except Exception as e:
                messagebox.showerror("错误", f"保存失败: {e}")
    
    def run(self):
        """运行主程序"""
        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            print("程序被用户中断")
        except Exception as e:
            messagebox.showerror("错误", f"程序运行出错: {e}")

def main():
    """主函数"""
    try:
        app = MouseCoordinateTracker()
        app.run()
    except ImportError as e:
        print("缺少必要的库，请安装：")
        print("pip install pyautogui pillow")
        print(f"错误详情: {e}")
    except Exception as e:
        print(f"程序启动失败: {e}")

if __name__ == "__main__":
    main()import tkinter as tk
from tkinter import messagebox, scrolledtext, filedialog
import threading
import time
import pyautogui
from datetime import datetime
from PIL import Image, ImageTk
import os

class MouseCoordinateTracker:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("鼠标坐标显示器 - OpenCV截图助手")
        self.root.geometry("500x700")
        self.root.configure(bg='#f0f0f0')
        
        # 设置窗口图标和属性
        self.root.attributes('-topmost', True)
        self.root.resizable(True, True)
        
        # 状态变量
        self.is_tracking = True
        self.is_topmost = True
        self.coordinate_list = []
        
        # 图片相关变量
        self.current_image = None
        self.image_path = None
        self.image_coordinates = []
        self.canvas_scale = 1.0
        self.canvas_offset_x = 0
        self.canvas_offset_y = 0
        
        # 颜色主题
        self.colors = {
            'bg': '#f0f0f0',
            'primary': '#2196F3',
            'secondary': '#4CAF50',
            'accent': '#FF9800',
            'danger': '#F44336',
            'text': '#333333'
        }
        
        # 禁用pyautogui安全机制
        pyautogui.FAILSAFE = False
        
        self.create_widgets()
        self.start_coordinate_tracking()
        
    def create_widgets(self):
        """创建界面组件"""
        # 主标题
        title_frame = tk.Frame(self.root, bg=self.colors['bg'])
        title_frame.pack(fill=tk.X, padx=20, pady=(20, 10))
        
        title_label = tk.Label(
            title_frame, 
            text="🖱️ 鼠标坐标追踪器", 
            font=('微软雅黑', 18, 'bold'),
            bg=self.colors['bg'],
            fg=self.colors['primary']
        )
        title_label.pack()
        
        # 当前坐标显示区域
        self.create_coordinate_display()
        
        # 控制按钮区域
        self.create_control_buttons()
        
        # 图片处理区域
        self.create_image_section()
        
        # 坐标记录区域
        self.create_record_section()
        
        # OpenCV代码生成区域
        self.create_code_generation_section()
        
        # 状态栏
        self.create_status_bar()
        
        # 绑定键盘事件
        self.bind_keyboard_events()
    
    def create_coordinate_display(self):
        """创建坐标显示区域"""
        coord_frame = tk.LabelFrame(
            self.root, 
            text="实时坐标", 
            font=('微软雅黑', 12, 'bold'),
            bg=self.colors['bg'],
            fg=self.colors['text'],
            padx=10,
            pady=10
        )
        coord_frame.pack(fill=tk.X, padx=20, pady=10)
        
        # 坐标显示
        self.coord_var = tk.StringVar(value="X: 0, Y: 0")
        coord_label = tk.Label(
            coord_frame,
            textvariable=self.coord_var,
            font=('Consolas', 16, 'bold'),
            bg='white',
            fg=self.colors['primary'],
            relief=tk.SUNKEN,
            padx=20,
            pady=10
        )
        coord_label.pack(fill=tk.X)
        
        # 屏幕信息
        screen_width, screen_height = pyautogui.size()
        screen_info = tk.Label(
            coord_frame,
            text=f"屏幕分辨率: {screen_width} × {screen_height}",
            font=('微软雅黑', 10),
            bg=self.colors['bg'],
            fg='gray'
        )
        screen_info.pack(pady=(5, 0))
    
    def create_control_buttons(self):
        """创建控制按钮"""
        control_frame = tk.LabelFrame(
            self.root,
            text="控制选项",
            font=('微软雅黑', 12, 'bold'),
            bg=self.colors['bg'],
            fg=self.colors['text'],
            padx=10,
            pady=10
        )
        control_frame.pack(fill=tk.X, padx=20, pady=10)
        
        # 按钮容器
        button_container = tk.Frame(control_frame, bg=self.colors['bg'])
        button_container.pack(fill=tk.X)
        
        # 追踪控制按钮
        self.tracking_btn = tk.Button(
            button_container,
            text="⏸️ 停止追踪",
            command=self.toggle_tracking,
            font=('微软雅黑', 10, 'bold'),
            bg=self.colors['danger'],
            fg='white',
            relief=tk.RAISED,
            padx=20,
            pady=8
        )
        self.tracking_btn.pack(side=tk.LEFT, padx=(0, 10), fill=tk.X, expand=True)
        
        # 置顶控制按钮
        self.topmost_btn = tk.Button(
            button_container,
            text="📌 取消置顶",
            command=self.toggle_topmost,
            font=('微软雅黑', 10, 'bold'),
            bg=self.colors['accent'],
            fg='white',
            relief=tk.RAISED,
            padx=20,
            pady=8
        )
        self.topmost_btn.pack(side=tk.LEFT, fill=tk.X, expand=True)
    
    def create_image_section(self):
        """创建图片处理区域"""
        image_frame = tk.LabelFrame(
            self.root,
            text="图片坐标选择",
            font=('微软雅黑', 12, 'bold'),
            bg=self.colors['bg'],
            fg=self.colors['text'],
            padx=10,
            pady=10
        )
        image_frame.pack(fill=tk.X, padx=20, pady=10)
        
        # 图片控制按钮
        img_control = tk.Frame(image_frame, bg=self.colors['bg'])
        img_control.pack(fill=tk.X, pady=(0, 10))
        
        load_img_btn = tk.Button(
            img_control,
            text="📁 加载图片",
            command=self.load_image,
            font=('微软雅黑', 10, 'bold'),
            bg=self.colors['primary'],
            fg='white',
            relief=tk.RAISED,
            padx=15,
            pady=5
        )
        load_img_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        clear_img_btn = tk.Button(
            img_control,
            text="🗑️ 清空图片坐标",
            command=self.clear_image_coordinates,
            font=('微软雅黑', 10, 'bold'),
            bg=self.colors['danger'],
            fg='white',
            relief=tk.RAISED,
            padx=15,
            pady=5
        )
        clear_img_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # 图片信息显示
        self.image_info_var = tk.StringVar(value="未加载图片")
        info_label = tk.Label(
            img_control,
            textvariable=self.image_info_var,
            font=('微软雅黑', 9),
            bg=self.colors['bg'],
            fg='gray'
        )
        info_label.pack(side=tk.LEFT, padx=(20, 0))
        
        # 图片显示画布容器
        canvas_container = tk.Frame(image_frame, bg=self.colors['bg'])
        canvas_container.pack(fill=tk.BOTH, expand=True)
        
        # 创建可滚动的画布
        canvas_frame = tk.Frame(canvas_container, bg=self.colors['bg'])
        canvas_frame.pack(fill=tk.BOTH, expand=True)
        
        # 画布
        self.image_canvas = tk.Canvas(
            canvas_frame,
            bg='white',
            relief=tk.SUNKEN,
            bd=2,
            width=400,
            height=300,
            scrollregion=(0, 0, 400, 300)
        )
        
        # 滚动条
        h_scrollbar = tk.Scrollbar(canvas_frame, orient=tk.HORIZONTAL, command=self.image_canvas.xview)
        v_scrollbar = tk.Scrollbar(canvas_frame, orient=tk.VERTICAL, command=self.image_canvas.yview)
        
        self.image_canvas.configure(xscrollcommand=h_scrollbar.set, yscrollcommand=v_scrollbar.set)
        
        # 布局
        self.image_canvas.grid(row=0, column=0, sticky="nsew")
        h_scrollbar.grid(row=1, column=0, sticky="ew")
        v_scrollbar.grid(row=0, column=1, sticky="ns")
        
        canvas_frame.grid_rowconfigure(0, weight=1)
        canvas_frame.grid_columnconfigure(0, weight=1)
        
        # 绑定画布点击事件
        self.image_canvas.bind('<Button-1>', self.on_image_click)
        self.image_canvas.bind('<MouseWheel>', self.on_canvas_scroll)
        
        # 图片坐标显示
        coord_display = tk.Frame(image_frame, bg=self.colors['bg'])
        coord_display.pack(fill=tk.X, pady=(10, 0))
        
        tk.Label(
            coord_display,
            text="图片坐标点:",
            font=('微软雅黑', 10, 'bold'),
            bg=self.colors['bg']
        ).pack(side=tk.LEFT)
        
        self.image_coords_var = tk.StringVar(value="未选择")
        tk.Label(
            coord_display,
            textvariable=self.image_coords_var,
            font=('Consolas', 10),
            bg=self.colors['bg'],
            fg=self.colors['primary']
        ).pack(side=tk.LEFT, padx=(10, 0))
    
    def create_record_section(self):
        """创建坐标记录区域"""
        record_frame = tk.LabelFrame(
            self.root,
            text="坐标记录",
            font=('微软雅黑', 12, 'bold'),
            bg=self.colors['bg'],
            fg=self.colors['text'],
            padx=10,
            pady=10
        )
        record_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
        
        # 记录控制按钮
        record_control = tk.Frame(record_frame, bg=self.colors['bg'])
        record_control.pack(fill=tk.X, pady=(0, 10))
        
        record_btn = tk.Button(
            record_control,
            text="📍 记录当前坐标",
            command=self.record_coordinate,
            font=('微软雅黑', 10, 'bold'),
            bg=self.colors['secondary'],
            fg='white',
            relief=tk.RAISED,
            padx=15,
            pady=5
        )
        record_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        clear_btn = tk.Button(
            record_control,
            text="🗑️ 清空记录",
            command=self.clear_records,
            font=('微软雅黑', 10, 'bold'),
            bg=self.colors['danger'],
            fg='white',
            relief=tk.RAISED,
            padx=15,
            pady=5
        )
        clear_btn.pack(side=tk.LEFT)
        
        # 坐标列表显示
        list_label = tk.Label(
            record_frame,
            text="已记录的坐标点:",
            font=('微软雅黑', 10, 'bold'),
            bg=self.colors['bg'],
            fg=self.colors['text']
        )
        list_label.pack(anchor=tk.W, pady=(0, 5))
        
        # 使用Listbox显示坐标
        listbox_frame = tk.Frame(record_frame, bg=self.colors['bg'])
        listbox_frame.pack(fill=tk.BOTH, expand=True)
        
        self.coord_listbox = tk.Listbox(
            listbox_frame,
            font=('Consolas', 10),
            bg='white',
            selectbackground=self.colors['primary'],
            height=8
        )
        self.coord_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # 滚动条
        scrollbar = tk.Scrollbar(listbox_frame, orient=tk.VERTICAL)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        self.coord_listbox.config(yscrollcommand=scrollbar.set)
        scrollbar.config(command=self.coord_listbox.yview)
        
        # 双击删除提示
        tip_label = tk.Label(
            record_frame,
            text="💡 提示: 双击列表项可删除单个坐标",
            font=('微软雅黑', 8),
            bg=self.colors['bg'],
            fg='gray'
        )
        tip_label.pack(anchor=tk.W, pady=(5, 0))
        
        # 绑定双击事件
        self.coord_listbox.bind('<Double-Button-1>', self.delete_selected_coordinate)
    
    def create_code_generation_section(self):
        """创建代码生成区域"""
        code_frame = tk.LabelFrame(
            self.root,
            text="OpenCV代码生成",
            font=('微软雅黑', 12, 'bold'),
            bg=self.colors['bg'],
            fg=self.colors['text'],
            padx=10,
            pady=10
        )
        code_frame.pack(fill=tk.X, padx=20, pady=10)
        
        generate_btn = tk.Button(
            code_frame,
            text="🔧 生成截图代码",
            command=self.generate_opencv_code,
            font=('微软雅黑', 11, 'bold'),
            bg=self.colors['primary'],
            fg='white',
            relief=tk.RAISED,
            padx=30,
            pady=10
        )
        generate_btn.pack()
    
    def create_status_bar(self):
        """创建状态栏"""
        status_frame = tk.Frame(self.root, bg='#e0e0e0', relief=tk.SUNKEN, bd=1)
        status_frame.pack(fill=tk.X, side=tk.BOTTOM)
        
        self.status_var = tk.StringVar(value="状态: 正在追踪鼠标坐标...")
        status_label = tk.Label(
            status_frame,
            textvariable=self.status_var,
            font=('微软雅黑', 9),
            bg='#e0e0e0',
            fg=self.colors['text'],
            anchor=tk.W
        )
        status_label.pack(side=tk.LEFT, padx=10, pady=2)
        
        # 快捷键提示
        shortcut_label = tk.Label(
            status_frame,
            text="快捷键: [空格]记录 [C]清空 [T]追踪 [P]置顶 [L]加载图片",
            font=('微软雅黑', 8),
            bg='#e0e0e0',
            fg='gray',
            anchor=tk.E
        )
        shortcut_label.pack(side=tk.RIGHT, padx=10, pady=2)
    
    def bind_keyboard_events(self):
        """绑定键盘事件"""
        self.root.bind('<KeyPress-space>', lambda e: self.record_coordinate())
        self.root.bind('<KeyPress-c>', lambda e: self.clear_records())
        self.root.bind('<KeyPress-t>', lambda e: self.toggle_tracking())
        self.root.bind('<KeyPress-p>', lambda e: self.toggle_topmost())
        self.root.bind('<KeyPress-l>', lambda e: self.load_image())
        
        # 确保窗口可以接收键盘事件
        self.root.focus_set()
    
    def start_coordinate_tracking(self):
        """启动坐标追踪线程"""
        def track_mouse():
            while True:
                if self.is_tracking:
                    try:
                        x, y = pyautogui.position()
                        self.coord_var.set(f"X: {x}, Y: {y}")
                    except Exception as e:
                        print(f"追踪错误: {e}")
                time.sleep(0.05)  # 提高刷新率
        
        tracking_thread = threading.Thread(target=track_mouse, daemon=True)
        tracking_thread.start()
    
    def toggle_tracking(self):
        """切换追踪状态"""
        self.is_tracking = not self.is_tracking
        
        if self.is_tracking:
            self.tracking_btn.config(
                text="⏸️ 停止追踪", 
                bg=self.colors['danger']
            )
            self.status_var.set("状态: 正在追踪鼠标坐标...")
        else:
            self.tracking_btn.config(
                text="▶️ 开始追踪", 
                bg=self.colors['secondary']
            )
            self.status_var.set("状态: 已停止追踪")
    
    def toggle_topmost(self):
        """切换窗口置顶状态"""
        self.is_topmost = not self.is_topmost
        self.root.attributes('-topmost', self.is_topmost)
        
        if self.is_topmost:
            self.topmost_btn.config(text="📌 取消置顶")
        else:
            self.topmost_btn.config(text="📌 设为置顶")
    
    def record_coordinate(self):
        """记录当前坐标"""
        try:
            x, y = pyautogui.position()
            timestamp = datetime.now().strftime("%H:%M:%S")
            
            coord_data = {
                'x': x,
                'y': y,
                'time': timestamp,
                'index': len(self.coordinate_list) + 1
            }
            
            self.coordinate_list.append(coord_data)
            
            # 添加到列表框
            display_text = f"{coord_data['index']:2d}. ({x:4d}, {y:4d}) - {timestamp}"
            self.coord_listbox.insert(tk.END, display_text)
            
            # 滚动到最新项
            self.coord_listbox.see(tk.END)
            
            # 更新状态
            count = len(self.coordinate_list)
            self.status_var.set(f"状态: 已记录坐标 ({x}, {y}) - 共 {count} 个点")
            
        except Exception as e:
            messagebox.showerror("错误", f"记录坐标失败: {e}")
    
    def delete_selected_coordinate(self, event):
        """删除选中的坐标"""
        selection = self.coord_listbox.curselection()
        if selection:
            index = selection[0]
            self.coord_listbox.delete(index)
            del self.coordinate_list[index]
            
            # 重新编号
            self.refresh_coordinate_list()
    
    def refresh_coordinate_list(self):
        """刷新坐标列表显示"""
        self.coord_listbox.delete(0, tk.END)
        
        for i, coord in enumerate(self.coordinate_list):
            coord['index'] = i + 1
            display_text = f"{coord['index']:2d}. ({coord['x']:4d}, {coord['y']:4d}) - {coord['time']}"
            self.coord_listbox.insert(tk.END, display_text)
    
    def clear_records(self):
        """清空所有记录"""
        if self.coordinate_list:
            result = messagebox.askyesno("确认", "确定要清空所有坐标记录吗？")
            if result:
                self.coordinate_list.clear()
                self.coord_listbox.delete(0, tk.END)
                self.status_var.set("状态: 已清空所有记录")
    
    def load_image(self):
        """加载图片"""
        file_types = [
            ("图片文件", "*.jpg;*.jpeg;*.png;*.bmp;*.gif;*.tiff"),
            ("JPEG文件", "*.jpg;*.jpeg"),
            ("PNG文件", "*.png"),
            ("所有文件", "*.*")
        ]
        
        file_path = filedialog.askopenfilename(
            title="选择图片文件",
            filetypes=file_types
        )
        
        if file_path:
            try:
                # 加载图片
                self.image_path = file_path
                self.current_image = Image.open(file_path)
                
                # 获取图片信息
                width, height = self.current_image.size
                file_size = os.path.getsize(file_path) / 1024  # KB
                
                # 更新信息显示
                filename = os.path.basename(file_path)
                self.image_info_var.set(f"{filename} ({width}×{height}, {file_size:.1f}KB)")
                
                # 显示图片
                self.display_image()
                
                # 清空之前的图片坐标
                self.image_coordinates.clear()
                self.update_image_coordinates_display()
                
                self.status_var.set(f"状态: 已加载图片 {filename}")
                
            except Exception as e:
                messagebox.showerror("错误", f"加载图片失败: {e}")
    
    def display_image(self):
        """在画布上显示图片"""
        if not self.current_image:
            return
        
        # 清空画布
        self.image_canvas.delete("all")
        
        # 计算显示尺寸
        canvas_width = 400
        canvas_height = 300
        img_width, img_height = self.current_image.size
        
        # 计算缩放比例
        scale_x = canvas_width / img_width
        scale_y = canvas_height / img_height
        self.canvas_scale = min(scale_x, scale_y, 1.0)  # 不放大，只缩小
        
        # 缩放图片
        display_width = int(img_width * self.canvas_scale)
        display_height = int(img_height * self.canvas_scale)
        
        display_image = self.current_image.resize((display_width, display_height), Image.Resampling.LANCZOS)
        self.photo = ImageTk.PhotoImage(display_image)
        
        # 计算居中位置
        self.canvas_offset_x = (canvas_width - display_width) // 2
        self.canvas_offset_y = (canvas_height - display_height) // 2
        
        # 显示图片
        self.image_canvas.create_image(
            self.canvas_offset_x, self.canvas_offset_y, 
            anchor=tk.NW, image=self.photo, tags="image"
        )
        
        # 更新滚动区域
        self.image_canvas.configure(scrollregion=self.image_canvas.bbox("all"))
        
        # 重绘已有的坐标点
        self.redraw_image_coordinates()
    
    def on_image_click(self, event):
        """处理图片点击事件"""
        if not self.current_image:
            return
        
        # 获取点击位置
        canvas_x = self.image_canvas.canvasx(event.x)
        canvas_y = self.image_canvas.canvasy(event.y)
        
        # 转换为图片坐标
        img_x = int((canvas_x - self.canvas_offset_x) / self.canvas_scale)
        img_y = int((canvas_y - self.canvas_offset_y) / self.canvas_scale)
        
        # 检查点击是否在图片范围内
        img_width, img_height = self.current_image.size
        if 0 <= img_x < img_width and 0 <= img_y < img_height:
            # 记录坐标
            timestamp = datetime.now().strftime("%H:%M:%S")
            coord_data = {
                'x': img_x,
                'y': img_y,
                'time': timestamp,
                'canvas_x': canvas_x,
                'canvas_y': canvas_y
            }
            
            self.image_coordinates.append(coord_data)
            
            # 在画布上绘制标记
            self.draw_coordinate_marker(canvas_x, canvas_y, len(self.image_coordinates))
            
            # 更新显示
            self.update_image_coordinates_display()
            
            self.status_var.set(f"状态: 在图片上记录坐标 ({img_x}, {img_y})")
    
    def draw_coordinate_marker(self, canvas_x, canvas_y, index):
        """在画布上绘制坐标标记"""
        colors = ['red', 'green', 'blue', 'orange', 'purple', 'brown']
        color = colors[(index - 1) % len(colors)]
        
        # 绘制圆形标记
        r = 5
        self.image_canvas.create_oval(
            canvas_x - r, canvas_y - r, canvas_x + r, canvas_y + r,
            fill=color, outline='white', width=2, tags="marker"
        )
        
        # 绘制编号
        self.image_canvas.create_text(
            canvas_x + 15, canvas_y - 15,
            text=str(index), fill=color, font=('Arial', 10, 'bold'),
            tags="marker"
        )
    
    def redraw_image_coordinates(self):
        """重新绘制所有坐标标记"""
        # 清除旧标记
        self.image_canvas.delete("marker")
        
        # 重绘所有标记
        for i, coord in enumerate(self.image_coordinates):
            # 重新计算画布坐标
            canvas_x = coord['x'] * self.canvas_scale + self.canvas_offset_x
            canvas_y = coord['y'] * self.canvas_scale + self.canvas_offset_y
            
            # 更新画布坐标
            coord['canvas_x'] = canvas_x
            coord['canvas_y'] = canvas_y
            
            # 绘制标记
            self.draw_coordinate_marker(canvas_x, canvas_y, i + 1)
    
    def update_image_coordinates_display(self):
        """更新图片坐标显示"""
        if not self.image_coordinates:
            self.image_coords_var.set("未选择")
        else:
            count = len(self.image_coordinates)
            last_coord = self.image_coordinates[-1]
            self.image_coords_var.set(
                f"共{count}个点，最新: ({last_coord['x']}, {last_coord['y']})"
            )
    
    def clear_image_coordinates(self):
        """清空图片坐标"""
        if self.image_coordinates:
            result = messagebox.askyesno("确认", "确定要清空图片上的所有坐标点吗？")
            if result:
                self.image_coordinates.clear()
                self.image_canvas.delete("marker")
                self.update_image_coordinates_display()
                self.status_var.set("状态: 已清空图片坐标")
    
    def on_canvas_scroll(self, event):
        """处理画布滚轮事件"""
        # 这里可以添加缩放功能
        pass
    
    def generate_opencv_code(self):
        """生成OpenCV代码"""
        if not self.coordinate_list:
            messagebox.showwarning("警告", "请先记录至少一个坐标点！")
            return
        
        # 创建代码显示窗口
        self.show_code_window()
    
    def show_code_window(self):
        """显示代码窗口"""
        code_window = tk.Toplevel(self.root)
        code_window.title("生成的OpenCV代码")
        code_window.geometry("800x600")
        code_window.configure(bg=self.colors['bg'])
        
        # 代码类型选择
        type_frame = tk.Frame(code_window, bg=self.colors['bg'])
        type_frame.pack(fill=tk.X, padx=20, pady=10)
        
        type_label = tk.Label(
            type_frame,
            text="代码类型:",
            font=('微软雅黑', 12, 'bold'),
            bg=self.colors['bg']
        )
        type_label.pack(side=tk.LEFT)
        
        # 根据坐标数量自动选择合适的代码类型
        screen_coord_count = len(self.coordinate_list)
        image_coord_count = len(self.image_coordinates)
        
        if image_coord_count > 0:
            if image_coord_count == 1:
                default_type = "image_single"
            elif image_coord_count == 2:
                default_type = "image_rectangle"
            else:
                default_type = "image_multiple"
        elif screen_coord_count == 1:
            default_type = "single"
        elif screen_coord_count == 2:
            default_type = "rectangle"
        else:
            default_type = "multiple"
        
        self.code_type = tk.StringVar(value=default_type)
        
        type_options = [
            ("屏幕单点标记", "single"),
            ("屏幕矩形截图", "rectangle"),
            ("屏幕多点标记", "multiple"),
            ("图片单点标记", "image_single"),
            ("图片矩形截图", "image_rectangle"),
            ("图片多点标记", "image_multiple")
        ]
        
        for text, value in type_options:
            rb = tk.Radiobutton(
                type_frame,
                text=text,
                variable=self.code_type,
                value=value,
                command=lambda: self.update_code_display(code_text),
                font=('微软雅黑', 10),
                bg=self.colors['bg']
            )
            rb.pack(side=tk.LEFT, padx=20)
        
        # 代码显示区域
        code_text = scrolledtext.ScrolledText(
            code_window,
            font=('Consolas', 10),
            bg='#2d2d2d',
            fg='#f8f8f2',
            insertbackground='white',
            wrap=tk.WORD
        )
        code_text.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
        
        # 按钮区域
        button_frame = tk.Frame(code_window, bg=self.colors['bg'])
        button_frame.pack(fill=tk.X, padx=20, pady=10)
        
        copy_btn = tk.Button(
            button_frame,
            text="📋 复制代码",
            command=lambda: self.copy_code_to_clipboard(code_text.get(1.0, tk.END)),
            font=('微软雅黑', 10, 'bold'),
            bg=self.colors['secondary'],
            fg='white',
            padx=20,
            pady=5
        )
        copy_btn.pack(side=tk.LEFT)
        
        save_btn = tk.Button(
            button_frame,
            text="💾 保存代码",
            command=lambda: self.save_code_to_file(code_text.get(1.0, tk.END)),
            font=('微软雅黑', 10, 'bold'),
            bg=self.colors['primary'],
            fg='white',
            padx=20,
            pady=5
        )
        save_btn.pack(side=tk.LEFT, padx=10)
        
        # 初始化代码显示
        self.update_code_display(code_text)
    
    def update_code_display(self, code_text):
        """更新代码显示"""
        code_text.delete(1.0, tk.END)
        generated_code = self.create_opencv_code()
        code_text.insert(1.0, generated_code)
    
    def create_opencv_code(self):
        """生成OpenCV代码"""
        code_type = self.code_type.get()
        screen_coords = self.coordinate_list
        image_coords = self.image_coordinates
        
        if code_type == "single" and screen_coords:
            return self.generate_single_point_code(screen_coords[0])
        elif code_type == "rectangle" and len(screen_coords) >= 2:
            return self.generate_rectangle_code(screen_coords[:2])
        elif code_type == "multiple" and screen_coords:
            return self.generate_multiple_points_code(screen_coords)
        elif code_type == "image_single" and image_coords:
            return self.generate_image_single_point_code(image_coords[0])
        elif code_type == "image_rectangle" and len(image_coords) >= 2:
            return self.generate_image_rectangle_code(image_coords[:2])
        elif code_type == "image_multiple" and image_coords:
            return self.generate_image_multiple_points_code(image_coords)
        else:
            return "# 请先记录相应数量的坐标点"
    
    def generate_single_point_code(self, coord):
        """生成单点标记代码"""
        x, y = coord['x'], coord['y']
        return f'''# 单点坐标标记代码
# 记录时间: {coord['time']}
import cv2
import numpy as np
from PIL import ImageGrab

# 目标坐标点
target_point = ({x}, {y})

# 截取屏幕
screenshot = ImageGrab.grab()
screenshot = np.array(screenshot)
screenshot = cv2.cvtColor(screenshot, cv2.COLOR_RGB2BGR)

# 在目标点绘制标记
cv2.circle(screenshot, target_point, 8, (0, 0, 255), -1)  # 红色实心圆
cv2.circle(screenshot, target_point, 15, (0, 255, 0), 2)  # 绿色圆环
cv2.putText(screenshot, f'({x}, {y})', (x + 20, y - 20), 
           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)

# 显示结果
cv2.imshow('Point Marker', screenshot)
cv2.waitKey(0)
cv2.destroyAllWindows()

# 保存截图
# cv2.imwrite('point_marker.png', screenshot)
'''
    
    def generate_rectangle_code(self, coords):
        """生成矩形截图代码"""
        x1, y1 = coords[0]['x'], coords[0]['y']
        x2, y2 = coords[1]['x'], coords[1]['y']
        
        left = min(x1, x2)
        top = min(y1, y2)
        right = max(x1, x2)
        bottom = max(y1, y2)
        width = right - left
        height = bottom - top
        
        return f'''# 矩形区域截图代码
# 点1记录时间: {coords[0]['time']}
# 点2记录时间: {coords[1]['time']}
import cv2
import numpy as np
from PIL import ImageGrab

# 原始坐标点
point1 = ({x1}, {y1})
point2 = ({x2}, {y2})

# 计算矩形边界
left, top = {left}, {top}
right, bottom = {right}, {bottom}
width, height = {width}, {height}

print(f"截图区域: 左上角({left}, {top}), 右下角({right}, {bottom})")
print(f"区域大小: {width} × {height} 像素")

# 方法1: 截取指定区域
cropped_screenshot = ImageGrab.grab(bbox=(left, top, right, bottom))
cropped_array = np.array(cropped_screenshot)
cropped_array = cv2.cvtColor(cropped_array, cv2.COLOR_RGB2BGR)

# 显示截取的区域
cv2.imshow('Cropped Region', cropped_array)

# 方法2: 全屏截图并标记区域
full_screenshot = ImageGrab.grab()
full_array = np.array(full_screenshot)
full_array = cv2.cvtColor(full_array, cv2.COLOR_RGB2BGR)

# 绘制矩形框
cv2.rectangle(full_array, (left, top), (right, bottom), (0, 255, 0), 3)
cv2.putText(full_array, f'{width}x{height}', (left, top-10), 
           cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 0), 2)

# 显示带标记的全屏截图
cv2.imshow('Full Screenshot with Marker', full_array)

cv2.waitKey(0)
cv2.destroyAllWindows()

# 保存截图
# cv2.imwrite('cropped_region.png', cropped_array)
# cv2.imwrite('marked_screenshot.png', full_array)
'''
    
    def generate_multiple_points_code(self, coords):
        """生成多点标记代码"""
        coords_list = [(coord['x'], coord['y']) for coord in coords]
        
        return f'''# 多点坐标标记代码
# 共记录 {len(coords)} 个坐标点
import cv2
import numpy as np
from PIL import ImageGrab

# 所有坐标点
coordinates = {coords_list}

# 截取屏幕
screenshot = ImageGrab.grab()
screenshot = np.array(screenshot)
screenshot = cv2.cvtColor(screenshot, cv2.COLOR_RGB2BGR)

# 定义颜色列表 (BGR格式)
colors = [
    (0, 0, 255),    # 红色
    (0, 255, 0),    # 绿色
    (255, 0, 0),    # 蓝色
    (0, 255, 255),  # 黄色
    (255, 0, 255),  # 紫色
    (255, 255, 0),  # 青色
]

# 标记每个坐标点
for i, (x, y) in enumerate(coordinates):
    color = colors[i % len(colors)]
    
    # 绘制标记点
    cv2.circle(screenshot, (x, y), 8, color, -1)
    cv2.circle(screenshot, (x, y), 15, (255, 255, 255), 2)
    
    # 添加标号
    cv2.putText(screenshot, f'{i+1}', (x-8, y+5), 
               cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
    
    # 添加坐标信息
    cv2.putText(screenshot, f'({x},{y})', (x+20, y-20), 
               cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2)

# 连接所有点形成路径
if len(coordinates) > 1:
    pts = np.array(coordinates, np.int32)
    cv2.polylines(screenshot, [pts], False, (255, 255, 255), 2)

# 计算并绘制边界框
if len(coordinates) >= 2:
    x_coords = [pt[0] for pt in coordinates]
    y_coords = [pt[1] for pt in coordinates]
    bbox_left, bbox_right = min(x_coords), max(x_coords)
    bbox_top, bbox_bottom = min(y_coords), max(y_coords)
    
    cv2.rectangle(screenshot, (bbox_left, bbox_top), 
                 (bbox_right, bbox_bottom), (0, 255, 255), 2)

# 显示结果
cv2.imshow('Multiple Points Marker', screenshot)
cv2.waitKey(0)
cv2.destroyAllWindows()

# 如需截取边界框区域
if len(coordinates) >= 2:
    bbox_cropped = ImageGrab.grab(bbox=(bbox_left, bbox_top, bbox_right, bbox_bottom))
    bbox_array = np.array(bbox_cropped)
    bbox_array = cv2.cvtColor(bbox_array, cv2.COLOR_RGB2BGR)
    
    cv2.imshow('Bounding Box Region', bbox_array)
    cv2.waitKey(0)
    cv2.destroyAllWindows()

# 保存截图
# cv2.imwrite('multiple_points.png', screenshot)
'''
    
    def generate_image_single_point_code(self, coord):
        """生成图片单点标记代码"""
        x, y = coord['x'], coord['y']
        return f'''# 图片单点坐标标记代码
# 图片路径: {self.image_path}
# 记录时间: {coord['time']}
import cv2
import numpy as np

# 加载图片
image_path = r"{self.image_path}"
image = cv2.imread(image_path)

if image is None:
    print(f"无法加载图片: {{image_path}}")
    exit()

# 目标坐标点
target_point = ({x}, {y})

# 在目标点绘制标记
cv2.circle(image, target_point, 8, (0, 0, 255), -1)  # 红色实心圆
cv2.circle(image, target_point, 15, (0, 255, 0), 2)  # 绿色圆环
cv2.putText(image, f'({x}, {y})', (x + 20, y - 20), 
           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)

# 显示结果
cv2.imshow('Image Point Marker', image)
cv2.waitKey(0)
cv2.destroyAllWindows()

# 保存标记后的图片
# cv2.imwrite('marked_image.png', image)
'''
    
    def generate_image_rectangle_code(self, coords):
        """生成图片矩形区域代码"""
        x1, y1 = coords[0]['x'], coords[0]['y']
        x2, y2 = coords[1]['x'], coords[1]['y']
        
        left = min(x1, x2)
        top = min(y1, y2)
        right = max(x1, x2)
        bottom = max(y1, y2)
        width = right - left
        height = bottom - top
        
        return f'''# 图片矩形区域处理代码
# 图片路径: {self.image_path}
# 点1记录时间: {coords[0]['time']}
# 点2记录时间: {coords[1]['time']}
import cv2
import numpy as np

# 加载图片
image_path = r"{self.image_path}"
original_image = cv2.imread(image_path)

if original_image is None:
    print(f"无法加载图片: {{image_path}}")
    exit()

# 原始坐标点
point1 = ({x1}, {y1})
point2 = ({x2}, {y2})

# 计算矩形边界
left, top = {left}, {top}
right, bottom = {right}, {bottom}
width, height = {width}, {height}

print(f"截取区域: 左上角({left}, {top}), 右下角({right}, {bottom})")
print(f"区域大小: {width} × {height} 像素")

# 方法1: 截取指定区域
cropped_region = original_image[top:bottom, left:right]

# 显示截取的区域
cv2.imshow('Cropped Region', cropped_region)

# 方法2: 在原图上标记区域
marked_image = original_image.copy()
cv2.rectangle(marked_image, (left, top), (right, bottom), (0, 255, 0), 3)
cv2.putText(marked_image, f'{width}x{height}', (left, top-10), 
           cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 0), 2)

# 显示带标记的原图
cv2.imshow('Original with Marker', marked_image)

cv2.waitKey(0)
cv2.destroyAllWindows()

# 保存结果
# cv2.imwrite('cropped_region.png', cropped_region)
# cv2.imwrite('marked_image.png', marked_image)
'''
    
    def generate_image_multiple_points_code(self, coords):
        """生成图片多点标记代码"""
        coords_list = [(coord['x'], coord['y']) for coord in coords]
        
        return f'''# 图片多点坐标标记代码
# 图片路径: {self.image_path}
# 共记录 {len(coords)} 个坐标点
import cv2
import numpy as np

# 加载图片
image_path = r"{self.image_path}"
image = cv2.imread(image_path)

if image is None:
    print(f"无法加载图片: {{image_path}}")
    exit()

# 所有坐标点
coordinates = {coords_list}

# 定义颜色列表 (BGR格式)
colors = [
    (0, 0, 255),    # 红色
    (0, 255, 0),    # 绿色
    (255, 0, 0),    # 蓝色
    (0, 255, 255),  # 黄色
    (255, 0, 255),  # 紫色
    (255, 255, 0),  # 青色
]

# 标记每个坐标点
for i, (x, y) in enumerate(coordinates):
    color = colors[i % len(colors)]
    
    # 绘制标记点
    cv2.circle(image, (x, y), 8, color, -1)
    cv2.circle(image, (x, y), 15, (255, 255, 255), 2)
    
    # 添加标号
    cv2.putText(image, f'{i+1}', (x-8, y+5), 
               cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
    
    # 添加坐标信息
    cv2.putText(image, f'({x},{y})', (x+20, y-20), 
               cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2)

# 连接所有点形成路径
if len(coordinates) > 1:
    pts = np.array(coordinates, np.int32)
    cv2.polylines(image, [pts], False, (255, 255, 255), 2)

# 计算并绘制边界框
if len(coordinates) >= 2:
    x_coords = [pt[0] for pt in coordinates]
    y_coords = [pt[1] for pt in coordinates]
    bbox_left, bbox_right = min(x_coords), max(x_coords)
    bbox_top, bbox_bottom = min(y_coords), max(y_coords)
    
    cv2.rectangle(image, (bbox_left, bbox_top), 
                 (bbox_right, bbox_bottom), (0, 255, 255), 2)

# 显示结果
cv2.imshow('Image Multiple Points Marker', image)
cv2.waitKey(0)
cv2.destroyAllWindows()

# 如需截取边界框区域
if len(coordinates) >= 2:
    bbox_cropped = image[bbox_top:bbox_bottom, bbox_left:bbox_right]
    cv2.imshow('Bounding Box Region', bbox_cropped)
    cv2.waitKey(0)
    cv2.destroyAllWindows()

# 保存标记后的图片
# cv2.imwrite('image_multiple_points.png', image)
'''
    
    def copy_code_to_clipboard(self, code):
        """复制代码到剪贴板"""
        self.root.clipboard_clear()
        self.root.clipboard_append(code)
        self.root.update()
        messagebox.showinfo("成功", "代码已复制到剪贴板！")
    
    def save_code_to_file(self, code):
        """保存代码到文件"""
        from tkinter import filedialog
        
        filename = filedialog.asksaveasfilename(
            defaultextension=".py",
            filetypes=[("Python文件", "*.py"), ("所有文件", "*.*")],
            title="保存OpenCV代码"
        )
        
        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(code)
                messagebox.showinfo("成功", f"代码已保存到: {filename}")
            except Exception as e:
                messagebox.showerror("错误", f"保存失败: {e}")
    
    def run(self):
        """运行主程序"""
        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            print("程序被用户中断")
        except Exception as e:
            messagebox.showerror("错误", f"程序运行出错: {e}")

def main():
    """主函数"""
    try:
        app = MouseCoordinateTracker()
        app.run()
    except ImportError as e:
        print("缺少必要的库，请安装：")
        print("pip install pyautogui pillow")
        print(f"错误详情: {e}")
    except Exception as e:
        print(f"程序启动失败: {e}")

if __name__ == "__main__":
    main()import tkinter as tk
from tkinter import messagebox, scrolledtext, filedialog
import threading
import time
import pyautogui
from datetime import datetime
from PIL import Image, ImageTk
import os

class MouseCoordinateTracker:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("鼠标坐标显示器 - OpenCV截图助手")
        self.root.geometry("500x700")
        self.root.configure(bg='#f0f0f0')
        
        # 设置窗口图标和属性
        self.root.attributes('-topmost', True)
        self.root.resizable(True, True)
        
        # 状态变量
        self.is_tracking = True
        self.is_topmost = True
        self.coordinate_list = []
        
        # 图片相关变量
        self.current_image = None
        self.image_path = None
        self.image_coordinates = []
        self.canvas_scale = 1.0
        self.canvas_offset_x = 0
        self.canvas_offset_y = 0
        
        # 颜色主题
        self.colors = {
            'bg': '#f0f0f0',
            'primary': '#2196F3',
            'secondary': '#4CAF50',
            'accent': '#FF9800',
            'danger': '#F44336',
            'text': '#333333'
        }
        
        # 禁用pyautogui安全机制
        pyautogui.FAILSAFE = False
        
        self.create_widgets()
        self.start_coordinate_tracking()
        
    def create_widgets(self):
        """创建界面组件"""
        # 主标题
        title_frame = tk.Frame(self.root, bg=self.colors['bg'])
        title_frame.pack(fill=tk.X, padx=20, pady=(20, 10))
        
        title_label = tk.Label(
            title_frame, 
            text="🖱️ 鼠标坐标追踪器", 
            font=('微软雅黑', 18, 'bold'),
            bg=self.colors['bg'],
            fg=self.colors['primary']
        )
        title_label.pack()
        
        # 当前坐标显示区域
        self.create_coordinate_display()
        
        # 控制按钮区域
        self.create_control_buttons()
        
        # 图片处理区域
        self.create_image_section()
        
        # 坐标记录区域
        self.create_record_section()
        
        # OpenCV代码生成区域
        self.create_code_generation_section()
        
        # 状态栏
        self.create_status_bar()
        
        # 绑定键盘事件
        self.bind_keyboard_events()
    
    def create_coordinate_display(self):
        """创建坐标显示区域"""
        coord_frame = tk.LabelFrame(
            self.root, 
            text="实时坐标", 
            font=('微软雅黑', 12, 'bold'),
            bg=self.colors['bg'],
            fg=self.colors['text'],
            padx=10,
            pady=10
        )
        coord_frame.pack(fill=tk.X, padx=20, pady=10)
        
        # 坐标显示
        self.coord_var = tk.StringVar(value="X: 0, Y: 0")
        coord_label = tk.Label(
            coord_frame,
            textvariable=self.coord_var,
            font=('Consolas', 16, 'bold'),
            bg='white',
            fg=self.colors['primary'],
            relief=tk.SUNKEN,
            padx=20,
            pady=10
        )
        coord_label.pack(fill=tk.X)
        
        # 屏幕信息
        screen_width, screen_height = pyautogui.size()
        screen_info = tk.Label(
            coord_frame,
            text=f"屏幕分辨率: {screen_width} × {screen_height}",
            font=('微软雅黑', 10),
            bg=self.colors['bg'],
            fg='gray'
        )
        screen_info.pack(pady=(5, 0))
    
    def create_control_buttons(self):
        """创建控制按钮"""
        control_frame = tk.LabelFrame(
            self.root,
            text="控制选项",
            font=('微软雅黑', 12, 'bold'),
            bg=self.colors['bg'],
            fg=self.colors['text'],
            padx=10,
            pady=10
        )
        control_frame.pack(fill=tk.X, padx=20, pady=10)
        
        # 按钮容器
        button_container = tk.Frame(control_frame, bg=self.colors['bg'])
        button_container.pack(fill=tk.X)
        
        # 追踪控制按钮
        self.tracking_btn = tk.Button(
            button_container,
            text="⏸️ 停止追踪",
            command=self.toggle_tracking,
            font=('微软雅黑', 10, 'bold'),
            bg=self.colors['danger'],
            fg='white',
            relief=tk.RAISED,
            padx=20,
            pady=8
        )
        self.tracking_btn.pack(side=tk.LEFT, padx=(0, 10), fill=tk.X, expand=True)
        
        # 置顶控制按钮
        self.topmost_btn = tk.Button(
            button_container,
            text="📌 取消置顶",
            command=self.toggle_topmost,
            font=('微软雅黑', 10, 'bold'),
            bg=self.colors['accent'],
            fg='white',
            relief=tk.RAISED,
            padx=20,
            pady=8
        )
        self.topmost_btn.pack(side=tk.LEFT, fill=tk.X, expand=True)
    
    def create_image_section(self):
        """创建图片处理区域"""
        image_frame = tk.LabelFrame(
            self.root,
            text="图片坐标选择",
            font=('微软雅黑', 12, 'bold'),
            bg=self.colors['bg'],
            fg=self.colors['text'],
            padx=10,
            pady=10
        )
        image_frame.pack(fill=tk.X, padx=20, pady=10)
        
        # 图片控制按钮
        img_control = tk.Frame(image_frame, bg=self.colors['bg'])
        img_control.pack(fill=tk.X, pady=(0, 10))
        
        load_img_btn = tk.Button(
            img_control,
            text="📁 加载图片",
            command=self.load_image,
            font=('微软雅黑', 10, 'bold'),
            bg=self.colors['primary'],
            fg='white',
            relief=tk.RAISED,
            padx=15,
            pady=5
        )
        load_img_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        clear_img_btn = tk.Button(
            img_control,
            text="🗑️ 清空图片坐标",
            command=self.clear_image_coordinates,
            font=('微软雅黑', 10, 'bold'),
            bg=self.colors['danger'],
            fg='white',
            relief=tk.RAISED,
            padx=15,
            pady=5
        )
        clear_img_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # 图片信息显示
        self.image_info_var = tk.StringVar(value="未加载图片")
        info_label = tk.Label(
            img_control,
            textvariable=self.image_info_var,
            font=('微软雅黑', 9),
            bg=self.colors['bg'],
            fg='gray'
        )
        info_label.pack(side=tk.LEFT, padx=(20, 0))
        
        # 图片显示画布容器
        canvas_container = tk.Frame(image_frame, bg=self.colors['bg'])
        canvas_container.pack(fill=tk.BOTH, expand=True)
        
        # 创建可滚动的画布
        canvas_frame = tk.Frame(canvas_container, bg=self.colors['bg'])
        canvas_frame.pack(fill=tk.BOTH, expand=True)
        
        # 画布
        self.image_canvas = tk.Canvas(
            canvas_frame,
            bg='white',
            relief=tk.SUNKEN,
            bd=2,
            width=400,
            height=300,
            scrollregion=(0, 0, 400, 300)
        )
        
        # 滚动条
        h_scrollbar = tk.Scrollbar(canvas_frame, orient=tk.HORIZONTAL, command=self.image_canvas.xview)
        v_scrollbar = tk.Scrollbar(canvas_frame, orient=tk.VERTICAL, command=self.image_canvas.yview)
        
        self.image_canvas.configure(xscrollcommand=h_scrollbar.set, yscrollcommand=v_scrollbar.set)
        
        # 布局
        self.image_canvas.grid(row=0, column=0, sticky="nsew")
        h_scrollbar.grid(row=1, column=0, sticky="ew")
        v_scrollbar.grid(row=0, column=1, sticky="ns")
        
        canvas_frame.grid_rowconfigure(0, weight=1)
        canvas_frame.grid_columnconfigure(0, weight=1)
        
        # 绑定画布点击事件
        self.image_canvas.bind('<Button-1>', self.on_image_click)
        self.image_canvas.bind('<MouseWheel>', self.on_canvas_scroll)
        
        # 图片坐标显示
        coord_display = tk.Frame(image_frame, bg=self.colors['bg'])
        coord_display.pack(fill=tk.X, pady=(10, 0))
        
        tk.Label(
            coord_display,
            text="图片坐标点:",
            font=('微软雅黑', 10, 'bold'),
            bg=self.colors['bg']
        ).pack(side=tk.LEFT)
        
        self.image_coords_var = tk.StringVar(value="未选择")
        tk.Label(
            coord_display,
            textvariable=self.image_coords_var,
            font=('Consolas', 10),
            bg=self.colors['bg'],
            fg=self.colors['primary']
        ).pack(side=tk.LEFT, padx=(10, 0))
    
    def create_record_section(self):
        """创建坐标记录区域"""
        record_frame = tk.LabelFrame(
            self.root,
            text="坐标记录",
            font=('微软雅黑', 12, 'bold'),
            bg=self.colors['bg'],
            fg=self.colors['text'],
            padx=10,
            pady=10
        )
        record_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
        
        # 记录控制按钮
        record_control = tk.Frame(record_frame, bg=self.colors['bg'])
        record_control.pack(fill=tk.X, pady=(0, 10))
        
        record_btn = tk.Button(
            record_control,
            text="📍 记录当前坐标",
            command=self.record_coordinate,
            font=('微软雅黑', 10, 'bold'),
            bg=self.colors['secondary'],
            fg='white',
            relief=tk.RAISED,
            padx=15,
            pady=5
        )
        record_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        clear_btn = tk.Button(
            record_control,
            text="🗑️ 清空记录",
            command=self.clear_records,
            font=('微软雅黑', 10, 'bold'),
            bg=self.colors['danger'],
            fg='white',
            relief=tk.RAISED,
            padx=15,
            pady=5
        )
        clear_btn.pack(side=tk.LEFT)
        
        # 坐标列表显示
        list_label = tk.Label(
            record_frame,
            text="已记录的坐标点:",
            font=('微软雅黑', 10, 'bold'),
            bg=self.colors['bg'],
            fg=self.colors['text']
        )
        list_label.pack(anchor=tk.W, pady=(0, 5))
        
        # 使用Listbox显示坐标
        listbox_frame = tk.Frame(record_frame, bg=self.colors['bg'])
        listbox_frame.pack(fill=tk.BOTH, expand=True)
        
        self.coord_listbox = tk.Listbox(
            listbox_frame,
            font=('Consolas', 10),
            bg='white',
            selectbackground=self.colors['primary'],
            height=8
        )
        self.coord_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # 滚动条
        scrollbar = tk.Scrollbar(listbox_frame, orient=tk.VERTICAL)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        self.coord_listbox.config(yscrollcommand=scrollbar.set)
        scrollbar.config(command=self.coord_listbox.yview)
        
        # 双击删除提示
        tip_label = tk.Label(
            record_frame,
            text="💡 提示: 双击列表项可删除单个坐标",
            font=('微软雅黑', 8),
            bg=self.colors['bg'],
            fg='gray'
        )
        tip_label.pack(anchor=tk.W, pady=(5, 0))
        
        # 绑定双击事件
        self.coord_listbox.bind('<Double-Button-1>', self.delete_selected_coordinate)
    
    def create_code_generation_section(self):
        """创建代码生成区域"""
        code_frame = tk.LabelFrame(
            self.root,
            text="OpenCV代码生成",
            font=('微软雅黑', 12, 'bold'),
            bg=self.colors['bg'],
            fg=self.colors['text'],
            padx=10,
            pady=10
        )
        code_frame.pack(fill=tk.X, padx=20, pady=10)
        
        generate_btn = tk.Button(
            code_frame,
            text="🔧 生成截图代码",
            command=self.generate_opencv_code,
            font=('微软雅黑', 11, 'bold'),
            bg=self.colors['primary'],
            fg='white',
            relief=tk.RAISED,
            padx=30,
            pady=10
        )
        generate_btn.pack()
    
    def create_status_bar(self):
        """创建状态栏"""
        status_frame = tk.Frame(self.root, bg='#e0e0e0', relief=tk.SUNKEN, bd=1)
        status_frame.pack(fill=tk.X, side=tk.BOTTOM)
        
        self.status_var = tk.StringVar(value="状态: 正在追踪鼠标坐标...")
        status_label = tk.Label(
            status_frame,
            textvariable=self.status_var,
            font=('微软雅黑', 9),
            bg='#e0e0e0',
            fg=self.colors['text'],
            anchor=tk.W
        )
        status_label.pack(side=tk.LEFT, padx=10, pady=2)
        
        # 快捷键提示
        shortcut_label = tk.Label(
            status_frame,
            text="快捷键: [空格]记录 [C]清空 [T]追踪 [P]置顶 [L]加载图片",
            font=('微软雅黑', 8),
            bg='#e0e0e0',
            fg='gray',
            anchor=tk.E
        )
        shortcut_label.pack(side=tk.RIGHT, padx=10, pady=2)
    
    def bind_keyboard_events(self):
        """绑定键盘事件"""
        self.root.bind('<KeyPress-space>', lambda e: self.record_coordinate())
        self.root.bind('<KeyPress-c>', lambda e: self.clear_records())
        self.root.bind('<KeyPress-t>', lambda e: self.toggle_tracking())
        self.root.bind('<KeyPress-p>', lambda e: self.toggle_topmost())
        self.root.bind('<KeyPress-l>', lambda e: self.load_image())
        
        # 确保窗口可以接收键盘事件
        self.root.focus_set()
    
    def start_coordinate_tracking(self):
        """启动坐标追踪线程"""
        def track_mouse():
            while True:
                if self.is_tracking:
                    try:
                        x, y = pyautogui.position()
                        self.coord_var.set(f"X: {x}, Y: {y}")
                    except Exception as e:
                        print(f"追踪错误: {e}")
                time.sleep(0.05)  # 提高刷新率
        
        tracking_thread = threading.Thread(target=track_mouse, daemon=True)
        tracking_thread.start()
    
    def toggle_tracking(self):
        """切换追踪状态"""
        self.is_tracking = not self.is_tracking
        
        if self.is_tracking:
            self.tracking_btn.config(
                text="⏸️ 停止追踪", 
                bg=self.colors['danger']
            )
            self.status_var.set("状态: 正在追踪鼠标坐标...")
        else:
            self.tracking_btn.config(
                text="▶️ 开始追踪", 
                bg=self.colors['secondary']
            )
            self.status_var.set("状态: 已停止追踪")
    
    def toggle_topmost(self):
        """切换窗口置顶状态"""
        self.is_topmost = not self.is_topmost
        self.root.attributes('-topmost', self.is_topmost)
        
        if self.is_topmost:
            self.topmost_btn.config(text="📌 取消置顶")
        else:
            self.topmost_btn.config(text="📌 设为置顶")
    
    def record_coordinate(self):
        """记录当前坐标"""
        try:
            x, y = pyautogui.position()
            timestamp = datetime.now().strftime("%H:%M:%S")
            
            coord_data = {
                'x': x,
                'y': y,
                'time': timestamp,
                'index': len(self.coordinate_list) + 1
            }
            
            self.coordinate_list.append(coord_data)
            
            # 添加到列表框
            display_text = f"{coord_data['index']:2d}. ({x:4d}, {y:4d}) - {timestamp}"
            self.coord_listbox.insert(tk.END, display_text)
            
            # 滚动到最新项
            self.coord_listbox.see(tk.END)
            
            # 更新状态
            count = len(self.coordinate_list)
            self.status_var.set(f"状态: 已记录坐标 ({x}, {y}) - 共 {count} 个点")
            
        except Exception as e:
            messagebox.showerror("错误", f"记录坐标失败: {e}")
    
    def delete_selected_coordinate(self, event):
        """删除选中的坐标"""
        selection = self.coord_listbox.curselection()
        if selection:
            index = selection[0]
            self.coord_listbox.delete(index)
            del self.coordinate_list[index]
            
            # 重新编号
            self.refresh_coordinate_list()
    
    def refresh_coordinate_list(self):
        """刷新坐标列表显示"""
        self.coord_listbox.delete(0, tk.END)
        
        for i, coord in enumerate(self.coordinate_list):
            coord['index'] = i + 1
            display_text = f"{coord['index']:2d}. ({coord['x']:4d}, {coord['y']:4d}) - {coord['time']}"
            self.coord_listbox.insert(tk.END, display_text)
    
    def clear_records(self):
        """清空所有记录"""
        if self.coordinate_list:
            result = messagebox.askyesno("确认", "确定要清空所有坐标记录吗？")
            if result:
                self.coordinate_list.clear()
                self.coord_listbox.delete(0, tk.END)
                self.status_var.set("状态: 已清空所有记录")
    
    def load_image(self):
        """加载图片"""
        file_types = [
            ("图片文件", "*.jpg;*.jpeg;*.png;*.bmp;*.gif;*.tiff"),
            ("JPEG文件", "*.jpg;*.jpeg"),
            ("PNG文件", "*.png"),
            ("所有文件", "*.*")
        ]
        
        file_path = filedialog.askopenfilename(
            title="选择图片文件",
            filetypes=file_types
        )
        
        if file_path:
            try:
                # 加载图片
                self.image_path = file_path
                self.current_image = Image.open(file_path)
                
                # 获取图片信息
                width, height = self.current_image.size
                file_size = os.path.getsize(file_path) / 1024  # KB
                
                # 更新信息显示
                filename = os.path.basename(file_path)
                self.image_info_var.set(f"{filename} ({width}×{height}, {file_size:.1f}KB)")
                
                # 显示图片
                self.display_image()
                
                # 清空之前的图片坐标
                self.image_coordinates.clear()
                self.update_image_coordinates_display()
                
                self.status_var.set(f"状态: 已加载图片 {filename}")
                
            except Exception as e:
                messagebox.showerror("错误", f"加载图片失败: {e}")
    
    def display_image(self):
        """在画布上显示图片"""
        if not self.current_image:
            return
        
        # 清空画布
        self.image_canvas.delete("all")
        
        # 计算显示尺寸
        canvas_width = 400
        canvas_height = 300
        img_width, img_height = self.current_image.size
        
        # 计算缩放比例
        scale_x = canvas_width / img_width
        scale_y = canvas_height / img_height
        self.canvas_scale = min(scale_x, scale_y, 1.0)  # 不放大，只缩小
        
        # 缩放图片
        display_width = int(img_width * self.canvas_scale)
        display_height = int(img_height * self.canvas_scale)
        
        display_image = self.current_image.resize((display_width, display_height), Image.Resampling.LANCZOS)
        self.photo = ImageTk.PhotoImage(display_image)
        
        # 计算居中位置
        self.canvas_offset_x = (canvas_width - display_width) // 2
        self.canvas_offset_y = (canvas_height - display_height) // 2
        
        # 显示图片
        self.image_canvas.create_image(
            self.canvas_offset_x, self.canvas_offset_y, 
            anchor=tk.NW, image=self.photo, tags="image"
        )
        
        # 更新滚动区域
        self.image_canvas.configure(scrollregion=self.image_canvas.bbox("all"))
        
        # 重绘已有的坐标点
        self.redraw_image_coordinates()
    
    def on_image_click(self, event):
        """处理图片点击事件"""
        if not self.current_image:
            return
        
        # 获取点击位置
        canvas_x = self.image_canvas.canvasx(event.x)
        canvas_y = self.image_canvas.canvasy(event.y)
        
        # 转换为图片坐标
        img_x = int((canvas_x - self.canvas_offset_x) / self.canvas_scale)
        img_y = int((canvas_y - self.canvas_offset_y) / self.canvas_scale)
        
        # 检查点击是否在图片范围内
        img_width, img_height = self.current_image.size
        if 0 <= img_x < img_width and 0 <= img_y < img_height:
            # 记录坐标
            timestamp = datetime.now().strftime("%H:%M:%S")
            coord_data = {
                'x': img_x,
                'y': img_y,
                'time': timestamp,
                'canvas_x': canvas_x,
                'canvas_y': canvas_y
            }
            
            self.image_coordinates.append(coord_data)
            
            # 在画布上绘制标记
            self.draw_coordinate_marker(canvas_x, canvas_y, len(self.image_coordinates))
            
            # 更新显示
            self.update_image_coordinates_display()
            
            self.status_var.set(f"状态: 在图片上记录坐标 ({img_x}, {img_y})")
    
    def draw_coordinate_marker(self, canvas_x, canvas_y, index):
        """在画布上绘制坐标标记"""
        colors = ['red', 'green', 'blue', 'orange', 'purple', 'brown']
        color = colors[(index - 1) % len(colors)]
        
        # 绘制圆形标记
        r = 5
        self.image_canvas.create_oval(
            canvas_x - r, canvas_y - r, canvas_x + r, canvas_y + r,
            fill=color, outline='white', width=2, tags="marker"
        )
        
        # 绘制编号
        self.image_canvas.create_text(
            canvas_x + 15, canvas_y - 15,
            text=str(index), fill=color, font=('Arial', 10, 'bold'),
            tags="marker"
        )
    
    def redraw_image_coordinates(self):
        """重新绘制所有坐标标记"""
        # 清除旧标记
        self.image_canvas.delete("marker")
        
        # 重绘所有标记
        for i, coord in enumerate(self.image_coordinates):
            # 重新计算画布坐标
            canvas_x = coord['x'] * self.canvas_scale + self.canvas_offset_x
            canvas_y = coord['y'] * self.canvas_scale + self.canvas_offset_y
            
            # 更新画布坐标
            coord['canvas_x'] = canvas_x
            coord['canvas_y'] = canvas_y
            
            # 绘制标记
            self.draw_coordinate_marker(canvas_x, canvas_y, i + 1)
    
    def update_image_coordinates_display(self):
        """更新图片坐标显示"""
        if not self.image_coordinates:
            self.image_coords_var.set("未选择")
        else:
            count = len(self.image_coordinates)
            last_coord = self.image_coordinates[-1]
            self.image_coords_var.set(
                f"共{count}个点，最新: ({last_coord['x']}, {last_coord['y']})"
            )
    
    def clear_image_coordinates(self):
        """清空图片坐标"""
        if self.image_coordinates:
            result = messagebox.askyesno("确认", "确定要清空图片上的所有坐标点吗？")
            if result:
                self.image_coordinates.clear()
                self.image_canvas.delete("marker")
                self.update_image_coordinates_display()
                self.status_var.set("状态: 已清空图片坐标")
    
    def on_canvas_scroll(self, event):
        """处理画布滚轮事件"""
        # 这里可以添加缩放功能
        pass
    
    def generate_opencv_code(self):
        """生成OpenCV代码"""
        if not self.coordinate_list:
            messagebox.showwarning("警告", "请先记录至少一个坐标点！")
            return
        
        # 创建代码显示窗口
        self.show_code_window()
    
    def show_code_window(self):
        """显示代码窗口"""
        code_window = tk.Toplevel(self.root)
        code_window.title("生成的OpenCV代码")
        code_window.geometry("800x600")
        code_window.configure(bg=self.colors['bg'])
        
        # 代码类型选择
        type_frame = tk.Frame(code_window, bg=self.colors['bg'])
        type_frame.pack(fill=tk.X, padx=20, pady=10)
        
        type_label = tk.Label(
            type_frame,
            text="代码类型:",
            font=('微软雅黑', 12, 'bold'),
            bg=self.colors['bg']
        )
        type_label.pack(side=tk.LEFT)
        
        # 根据坐标数量自动选择合适的代码类型
        screen_coord_count = len(self.coordinate_list)
        image_coord_count = len(self.image_coordinates)
        
        if image_coord_count > 0:
            if image_coord_count == 1:
                default_type = "image_single"
            elif image_coord_count == 2:
                default_type = "image_rectangle"
            else:
                default_type = "image_multiple"
        elif screen_coord_count == 1:
            default_type = "single"
        elif screen_coord_count == 2:
            default_type = "rectangle"
        else:
            default_type = "multiple"
        
        self.code_type = tk.StringVar(value=default_type)
        
        type_options = [
            ("屏幕单点标记", "single"),
            ("屏幕矩形截图", "rectangle"),
            ("屏幕多点标记", "multiple"),
            ("图片单点标记", "image_single"),
            ("图片矩形截图", "image_rectangle"),
            ("图片多点标记", "image_multiple")
        ]
        
        for text, value in type_options:
            rb = tk.Radiobutton(
                type_frame,
                text=text,
                variable=self.code_type,
                value=value,
                command=lambda: self.update_code_display(code_text),
                font=('微软雅黑', 10),
                bg=self.colors['bg']
            )
            rb.pack(side=tk.LEFT, padx=20)
        
        # 代码显示区域
        code_text = scrolledtext.ScrolledText(
            code_window,
            font=('Consolas', 10),
            bg='#2d2d2d',
            fg='#f8f8f2',
            insertbackground='white',
            wrap=tk.WORD
        )
        code_text.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
        
        # 按钮区域
        button_frame = tk.Frame(code_window, bg=self.colors['bg'])
        button_frame.pack(fill=tk.X, padx=20, pady=10)
        
        copy_btn = tk.Button(
            button_frame,
            text="📋 复制代码",
            command=lambda: self.copy_code_to_clipboard(code_text.get(1.0, tk.END)),
            font=('微软雅黑', 10, 'bold'),
            bg=self.colors['secondary'],
            fg='white',
            padx=20,
            pady=5
        )
        copy_btn.pack(side=tk.LEFT)
        
        save_btn = tk.Button(
            button_frame,
            text="💾 保存代码",
            command=lambda: self.save_code_to_file(code_text.get(1.0, tk.END)),
            font=('微软雅黑', 10, 'bold'),
            bg=self.colors['primary'],
            fg='white',
            padx=20,
            pady=5
        )
        save_btn.pack(side=tk.LEFT, padx=10)
        
        # 初始化代码显示
        self.update_code_display(code_text)
    
    def update_code_display(self, code_text):
        """更新代码显示"""
        code_text.delete(1.0, tk.END)
        generated_code = self.create_opencv_code()
        code_text.insert(1.0, generated_code)
    
    def create_opencv_code(self):
        """生成OpenCV代码"""
        code_type = self.code_type.get()
        screen_coords = self.coordinate_list
        image_coords = self.image_coordinates
        
        if code_type == "single" and screen_coords:
            return self.generate_single_point_code(screen_coords[0])
        elif code_type == "rectangle" and len(screen_coords) >= 2:
            return self.generate_rectangle_code(screen_coords[:2])
        elif code_type == "multiple" and screen_coords:
            return self.generate_multiple_points_code(screen_coords)
        elif code_type == "image_single" and image_coords:
            return self.generate_image_single_point_code(image_coords[0])
        elif code_type == "image_rectangle" and len(image_coords) >= 2:
            return self.generate_image_rectangle_code(image_coords[:2])
        elif code_type == "image_multiple" and image_coords:
            return self.generate_image_multiple_points_code(image_coords)
        else:
            return "# 请先记录相应数量的坐标点"
    
    def generate_single_point_code(self, coord):
        """生成单点标记代码"""
        x, y = coord['x'], coord['y']
        return f'''# 单点坐标标记代码
# 记录时间: {coord['time']}
import cv2
import numpy as np
from PIL import ImageGrab

# 目标坐标点
target_point = ({x}, {y})

# 截取屏幕
screenshot = ImageGrab.grab()
screenshot = np.array(screenshot)
screenshot = cv2.cvtColor(screenshot, cv2.COLOR_RGB2BGR)

# 在目标点绘制标记
cv2.circle(screenshot, target_point, 8, (0, 0, 255), -1)  # 红色实心圆
cv2.circle(screenshot, target_point, 15, (0, 255, 0), 2)  # 绿色圆环
cv2.putText(screenshot, f'({x}, {y})', (x + 20, y - 20), 
           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)

# 显示结果
cv2.imshow('Point Marker', screenshot)
cv2.waitKey(0)
cv2.destroyAllWindows()

# 保存截图
# cv2.imwrite('point_marker.png', screenshot)
'''
    
    def generate_rectangle_code(self, coords):
        """生成矩形截图代码"""
        x1, y1 = coords[0]['x'], coords[0]['y']
        x2, y2 = coords[1]['x'], coords[1]['y']
        
        left = min(x1, x2)
        top = min(y1, y2)
        right = max(x1, x2)
        bottom = max(y1, y2)
        width = right - left
        height = bottom - top
        
        return f'''# 矩形区域截图代码
# 点1记录时间: {coords[0]['time']}
# 点2记录时间: {coords[1]['time']}
import cv2
import numpy as np
from PIL import ImageGrab

# 原始坐标点
point1 = ({x1}, {y1})
point2 = ({x2}, {y2})

# 计算矩形边界
left, top = {left}, {top}
right, bottom = {right}, {bottom}
width, height = {width}, {height}

print(f"截图区域: 左上角({left}, {top}), 右下角({right}, {bottom})")
print(f"区域大小: {width} × {height} 像素")

# 方法1: 截取指定区域
cropped_screenshot = ImageGrab.grab(bbox=(left, top, right, bottom))
cropped_array = np.array(cropped_screenshot)
cropped_array = cv2.cvtColor(cropped_array, cv2.COLOR_RGB2BGR)

# 显示截取的区域
cv2.imshow('Cropped Region', cropped_array)

# 方法2: 全屏截图并标记区域
full_screenshot = ImageGrab.grab()
full_array = np.array(full_screenshot)
full_array = cv2.cvtColor(full_array, cv2.COLOR_RGB2BGR)

# 绘制矩形框
cv2.rectangle(full_array, (left, top), (right, bottom), (0, 255, 0), 3)
cv2.putText(full_array, f'{width}x{height}', (left, top-10), 
           cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 0), 2)

# 显示带标记的全屏截图
cv2.imshow('Full Screenshot with Marker', full_array)

cv2.waitKey(0)
cv2.destroyAllWindows()

# 保存截图
# cv2.imwrite('cropped_region.png', cropped_array)
# cv2.imwrite('marked_screenshot.png', full_array)
'''
    
    def generate_multiple_points_code(self, coords):
        """生成多点标记代码"""
        coords_list = [(coord['x'], coord['y']) for coord in coords]
        
        return f'''# 多点坐标标记代码
# 共记录 {len(coords)} 个坐标点
import cv2
import numpy as np
from PIL import ImageGrab

# 所有坐标点
coordinates = {coords_list}

# 截取屏幕
screenshot = ImageGrab.grab()
screenshot = np.array(screenshot)
screenshot = cv2.cvtColor(screenshot, cv2.COLOR_RGB2BGR)

# 定义颜色列表 (BGR格式)
colors = [
    (0, 0, 255),    # 红色
    (0, 255, 0),    # 绿色
    (255, 0, 0),    # 蓝色
    (0, 255, 255),  # 黄色
    (255, 0, 255),  # 紫色
    (255, 255, 0),  # 青色
]

# 标记每个坐标点
for i, (x, y) in enumerate(coordinates):
    color = colors[i % len(colors)]
    
    # 绘制标记点
    cv2.circle(screenshot, (x, y), 8, color, -1)
    cv2.circle(screenshot, (x, y), 15, (255, 255, 255), 2)
    
    # 添加标号
    cv2.putText(screenshot, f'{i+1}', (x-8, y+5), 
               cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
    
    # 添加坐标信息
    cv2.putText(screenshot, f'({x},{y})', (x+20, y-20), 
               cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2)

# 连接所有点形成路径
if len(coordinates) > 1:
    pts = np.array(coordinates, np.int32)
    cv2.polylines(screenshot, [pts], False, (255, 255, 255), 2)

# 计算并绘制边界框
if len(coordinates) >= 2:
    x_coords = [pt[0] for pt in coordinates]
    y_coords = [pt[1] for pt in coordinates]
    bbox_left, bbox_right = min(x_coords), max(x_coords)
    bbox_top, bbox_bottom = min(y_coords), max(y_coords)
    
    cv2.rectangle(screenshot, (bbox_left, bbox_top), 
                 (bbox_right, bbox_bottom), (0, 255, 255), 2)

# 显示结果
cv2.imshow('Multiple Points Marker', screenshot)
cv2.waitKey(0)
cv2.destroyAllWindows()

# 如需截取边界框区域
if len(coordinates) >= 2:
    bbox_cropped = ImageGrab.grab(bbox=(bbox_left, bbox_top, bbox_right, bbox_bottom))
    bbox_array = np.array(bbox_cropped)
    bbox_array = cv2.cvtColor(bbox_array, cv2.COLOR_RGB2BGR)
    
    cv2.imshow('Bounding Box Region', bbox_array)
    cv2.waitKey(0)
    cv2.destroyAllWindows()

# 保存截图
# cv2.imwrite('multiple_points.png', screenshot)
'''
    
    def generate_image_single_point_code(self, coord):
        """生成图片单点标记代码"""
        x, y = coord['x'], coord['y']
        return f'''# 图片单点坐标标记代码
# 图片路径: {self.image_path}
# 记录时间: {coord['time']}
import cv2
import numpy as np

# 加载图片
image_path = r"{self.image_path}"
image = cv2.imread(image_path)

if image is None:
    print(f"无法加载图片: {{image_path}}")
    exit()

# 目标坐标点
target_point = ({x}, {y})

# 在目标点绘制标记
cv2.circle(image, target_point, 8, (0, 0, 255), -1)  # 红色实心圆
cv2.circle(image, target_point, 15, (0, 255, 0), 2)  # 绿色圆环
cv2.putText(image, f'({x}, {y})', (x + 20, y - 20), 
           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)

# 显示结果
cv2.imshow('Image Point Marker', image)
cv2.waitKey(0)
cv2.destroyAllWindows()

# 保存标记后的图片
# cv2.imwrite('marked_image.png', image)
'''
    
    def generate_image_rectangle_code(self, coords):
        """生成图片矩形区域代码"""
        x1, y1 = coords[0]['x'], coords[0]['y']
        x2, y2 = coords[1]['x'], coords[1]['y']
        
        left = min(x1, x2)
        top = min(y1, y2)
        right = max(x1, x2)
        bottom = max(y1, y2)
        width = right - left
        height = bottom - top
        
        return f'''# 图片矩形区域处理代码
# 图片路径: {self.image_path}
# 点1记录时间: {coords[0]['time']}
# 点2记录时间: {coords[1]['time']}
import cv2
import numpy as np

# 加载图片
image_path = r"{self.image_path}"
original_image = cv2.imread(image_path)

if original_image is None:
    print(f"无法加载图片: {{image_path}}")
    exit()

# 原始坐标点
point1 = ({x1}, {y1})
point2 = ({x2}, {y2})

# 计算矩形边界
left, top = {left}, {top}
right, bottom = {right}, {bottom}
width, height = {width}, {height}

print(f"截取区域: 左上角({left}, {top}), 右下角({right}, {bottom})")
print(f"区域大小: {width} × {height} 像素")

# 方法1: 截取指定区域
cropped_region = original_image[top:bottom, left:right]

# 显示截取的区域
cv2.imshow('Cropped Region', cropped_region)

# 方法2: 在原图上标记区域
marked_image = original_image.copy()
cv2.rectangle(marked_image, (left, top), (right, bottom), (0, 255, 0), 3)
cv2.putText(marked_image, f'{width}x{height}', (left, top-10), 
           cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 0), 2)

# 显示带标记的原图
cv2.imshow('Original with Marker', marked_image)

cv2.waitKey(0)
cv2.destroyAllWindows()

# 保存结果
# cv2.imwrite('cropped_region.png', cropped_region)
# cv2.imwrite('marked_image.png', marked_image)
'''
    
    def generate_image_multiple_points_code(self, coords):
        """生成图片多点标记代码"""
        coords_list = [(coord['x'], coord['y']) for coord in coords]
        
        return f'''# 图片多点坐标标记代码
# 图片路径: {self.image_path}
# 共记录 {len(coords)} 个坐标点
import cv2
import numpy as np

# 加载图片
image_path = r"{self.image_path}"
image = cv2.imread(image_path)

if image is None:
    print(f"无法加载图片: {{image_path}}")
    exit()

# 所有坐标点
coordinates = {coords_list}

# 定义颜色列表 (BGR格式)
colors = [
    (0, 0, 255),    # 红色
    (0, 255, 0),    # 绿色
    (255, 0, 0),    # 蓝色
    (0, 255, 255),  # 黄色
    (255, 0, 255),  # 紫色
    (255, 255, 0),  # 青色
]

# 标记每个坐标点
for i, (x, y) in enumerate(coordinates):
    color = colors[i % len(colors)]
    
    # 绘制标记点
    cv2.circle(image, (x, y), 8, color, -1)
    cv2.circle(image, (x, y), 15, (255, 255, 255), 2)
    
    # 添加标号
    cv2.putText(image, f'{i+1}', (x-8, y+5), 
               cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
    
    # 添加坐标信息
    cv2.putText(image, f'({x},{y})', (x+20, y-20), 
               cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2)

# 连接所有点形成路径
if len(coordinates) > 1:
    pts = np.array(coordinates, np.int32)
    cv2.polylines(image, [pts], False, (255, 255, 255), 2)

# 计算并绘制边界框
if len(coordinates) >= 2:
    x_coords = [pt[0] for pt in coordinates]
    y_coords = [pt[1] for pt in coordinates]
    bbox_left, bbox_right = min(x_coords), max(x_coords)
    bbox_top, bbox_bottom = min(y_coords), max(y_coords)
    
    cv2.rectangle(image, (bbox_left, bbox_top), 
                 (bbox_right, bbox_bottom), (0, 255, 255), 2)

# 显示结果
cv2.imshow('Image Multiple Points Marker', image)
cv2.waitKey(0)
cv2.destroyAllWindows()

# 如需截取边界框区域
if len(coordinates) >= 2:
    bbox_cropped = image[bbox_top:bbox_bottom, bbox_left:bbox_right]
    cv2.imshow('Bounding Box Region', bbox_cropped)
    cv2.waitKey(0)
    cv2.destroyAllWindows()

# 保存标记后的图片
# cv2.imwrite('image_multiple_points.png', image)
'''
    
    def copy_code_to_clipboard(self, code):
        """复制代码到剪贴板"""
        self.root.clipboard_clear()
        self.root.clipboard_append(code)
        self.root.update()
        messagebox.showinfo("成功", "代码已复制到剪贴板！")
    
    def save_code_to_file(self, code):
        """保存代码到文件"""
        from tkinter import filedialog
        
        filename = filedialog.asksaveasfilename(
            defaultextension=".py",
            filetypes=[("Python文件", "*.py"), ("所有文件", "*.*")],
            title="保存OpenCV代码"
        )
        
        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(code)
                messagebox.showinfo("成功", f"代码已保存到: {filename}")
            except Exception as e:
                messagebox.showerror("错误", f"保存失败: {e}")
    
    def run(self):
        """运行主程序"""
        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            print("程序被用户中断")
        except Exception as e:
            messagebox.showerror("错误", f"程序运行出错: {e}")

def main():
    """主函数"""
    try:
        app = MouseCoordinateTracker()
        app.run()
    except ImportError as e:
        print("缺少必要的库，请安装：")
        print("pip install pyautogui pillow")
        print(f"错误详情: {e}")
    except Exception as e:
        print(f"程序启动失败: {e}")

if __name__ == "__main__":
    main()import tkinter as tk
from tkinter import messagebox, scrolledtext, filedialog
import threading
import time
import pyautogui
from datetime import datetime
from PIL import Image, ImageTk
import os

class MouseCoordinateTracker:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("鼠标坐标显示器 - OpenCV截图助手")
        self.root.geometry("500x700")
        self.root.configure(bg='#f0f0f0')
        
        # 设置窗口图标和属性
        self.root.attributes('-topmost', True)
        self.root.resizable(True, True)
        
        # 状态变量
        self.is_tracking = True
        self.is_topmost = True
        self.coordinate_list = []
        
        # 图片相关变量
        self.current_image = None
        self.image_path = None
        self.image_coordinates = []
        self.canvas_scale = 1.0
        self.canvas_offset_x = 0
        self.canvas_offset_y = 0
        
        # 颜色主题
        self.colors = {
            'bg': '#f0f0f0',
            'primary': '#2196F3',
            'secondary': '#4CAF50',
            'accent': '#FF9800',
            'danger': '#F44336',
            'text': '#333333'
        }
        
        # 禁用pyautogui安全机制
        pyautogui.FAILSAFE = False
        
        self.create_widgets()
        self.start_coordinate_tracking()
        
    def create_widgets(self):
        """创建界面组件"""
        # 主标题
        title_frame = tk.Frame(self.root, bg=self.colors['bg'])
        title_frame.pack(fill=tk.X, padx=20, pady=(20, 10))
        
        title_label = tk.Label(
            title_frame, 
            text="🖱️ 鼠标坐标追踪器", 
            font=('微软雅黑', 18, 'bold'),
            bg=self.colors['bg'],
            fg=self.colors['primary']
        )
        title_label.pack()
        
        # 当前坐标显示区域
        self.create_coordinate_display()
        
        # 控制按钮区域
        self.create_control_buttons()
        
        # 图片处理区域
        self.create_image_section()
        
        # 坐标记录区域
        self.create_record_section()
        
        # OpenCV代码生成区域
        self.create_code_generation_section()
        
        # 状态栏
        self.create_status_bar()
        
        # 绑定键盘事件
        self.bind_keyboard_events()
    
    def create_coordinate_display(self):
        """创建坐标显示区域"""
        coord_frame = tk.LabelFrame(
            self.root, 
            text="实时坐标", 
            font=('微软雅黑', 12, 'bold'),
            bg=self.colors['bg'],
            fg=self.colors['text'],
            padx=10,
            pady=10
        )
        coord_frame.pack(fill=tk.X, padx=20, pady=10)
        
        # 坐标显示
        self.coord_var = tk.StringVar(value="X: 0, Y: 0")
        coord_label = tk.Label(
            coord_frame,
            textvariable=self.coord_var,
            font=('Consolas', 16, 'bold'),
            bg='white',
            fg=self.colors['primary'],
            relief=tk.SUNKEN,
            padx=20,
            pady=10
        )
        coord_label.pack(fill=tk.X)
        
        # 屏幕信息
        screen_width, screen_height = pyautogui.size()
        screen_info = tk.Label(
            coord_frame,
            text=f"屏幕分辨率: {screen_width} × {screen_height}",
            font=('微软雅黑', 10),
            bg=self.colors['bg'],
            fg='gray'
        )
        screen_info.pack(pady=(5, 0))
    
    def create_control_buttons(self):
        """创建控制按钮"""
        control_frame = tk.LabelFrame(
            self.root,
            text="控制选项",
            font=('微软雅黑', 12, 'bold'),
            bg=self.colors['bg'],
            fg=self.colors['text'],
            padx=10,
            pady=10
        )
        control_frame.pack(fill=tk.X, padx=20, pady=10)
        
        # 按钮容器
        button_container = tk.Frame(control_frame, bg=self.colors['bg'])
        button_container.pack(fill=tk.X)
        
        # 追踪控制按钮
        self.tracking_btn = tk.Button(
            button_container,
            text="⏸️ 停止追踪",
            command=self.toggle_tracking,
            font=('微软雅黑', 10, 'bold'),
            bg=self.colors['danger'],
            fg='white',
            relief=tk.RAISED,
            padx=20,
            pady=8
        )
        self.tracking_btn.pack(side=tk.LEFT, padx=(0, 10), fill=tk.X, expand=True)
        
        # 置顶控制按钮
        self.topmost_btn = tk.Button(
            button_container,
            text="📌 取消置顶",
            command=self.toggle_topmost,
            font=('微软雅黑', 10, 'bold'),
            bg=self.colors['accent'],
            fg='white',
            relief=tk.RAISED,
            padx=20,
            pady=8
        )
        self.topmost_btn.pack(side=tk.LEFT, fill=tk.X, expand=True)
    
    def create_image_section(self):
        """创建图片处理区域"""
        image_frame = tk.LabelFrame(
            self.root,
            text="图片坐标选择",
            font=('微软雅黑', 12, 'bold'),
            bg=self.colors['bg'],
            fg=self.colors['text'],
            padx=10,
            pady=10
        )
        image_frame.pack(fill=tk.X, padx=20, pady=10)
        
        # 图片控制按钮
        img_control = tk.Frame(image_frame, bg=self.colors['bg'])
        img_control.pack(fill=tk.X, pady=(0, 10))
        
        load_img_btn = tk.Button(
            img_control,
            text="📁 加载图片",
            command=self.load_image,
            font=('微软雅黑', 10, 'bold'),
            bg=self.colors['primary'],
            fg='white',
            relief=tk.RAISED,
            padx=15,
            pady=5
        )
        load_img_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        clear_img_btn = tk.Button(
            img_control,
            text="🗑️ 清空图片坐标",
            command=self.clear_image_coordinates,
            font=('微软雅黑', 10, 'bold'),
            bg=self.colors['danger'],
            fg='white',
            relief=tk.RAISED,
            padx=15,
            pady=5
        )
        clear_img_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # 图片信息显示
        self.image_info_var = tk.StringVar(value="未加载图片")
        info_label = tk.Label(
            img_control,
            textvariable=self.image_info_var,
            font=('微软雅黑', 9),
            bg=self.colors['bg'],
            fg='gray'
        )
        info_label.pack(side=tk.LEFT, padx=(20, 0))
        
        # 图片显示画布容器
        canvas_container = tk.Frame(image_frame, bg=self.colors['bg'])
        canvas_container.pack(fill=tk.BOTH, expand=True)
        
        # 创建可滚动的画布
        canvas_frame = tk.Frame(canvas_container, bg=self.colors['bg'])
        canvas_frame.pack(fill=tk.BOTH, expand=True)
        
        # 画布
        self.image_canvas = tk.Canvas(
            canvas_frame,
            bg='white',
            relief=tk.SUNKEN,
            bd=2,
            width=400,
            height=300,
            scrollregion=(0, 0, 400, 300)
        )
        
        # 滚动条
        h_scrollbar = tk.Scrollbar(canvas_frame, orient=tk.HORIZONTAL, command=self.image_canvas.xview)
        v_scrollbar = tk.Scrollbar(canvas_frame, orient=tk.VERTICAL, command=self.image_canvas.yview)
        
        self.image_canvas.configure(xscrollcommand=h_scrollbar.set, yscrollcommand=v_scrollbar.set)
        
        # 布局
        self.image_canvas.grid(row=0, column=0, sticky="nsew")
        h_scrollbar.grid(row=1, column=0, sticky="ew")
        v_scrollbar.grid(row=0, column=1, sticky="ns")
        
        canvas_frame.grid_rowconfigure(0, weight=1)
        canvas_frame.grid_columnconfigure(0, weight=1)
        
        # 绑定画布点击事件
        self.image_canvas.bind('<Button-1>', self.on_image_click)
        self.image_canvas.bind('<MouseWheel>', self.on_canvas_scroll)
        
        # 图片坐标显示
        coord_display = tk.Frame(image_frame, bg=self.colors['bg'])
        coord_display.pack(fill=tk.X, pady=(10, 0))
        
        tk.Label(
            coord_display,
            text="图片坐标点:",
            font=('微软雅黑', 10, 'bold'),
            bg=self.colors['bg']
        ).pack(side=tk.LEFT)
        
        self.image_coords_var = tk.StringVar(value="未选择")
        tk.Label(
            coord_display,
            textvariable=self.image_coords_var,
            font=('Consolas', 10),
            bg=self.colors['bg'],
            fg=self.colors['primary']
        ).pack(side=tk.LEFT, padx=(10, 0))
    
    def create_record_section(self):
        """创建坐标记录区域"""
        record_frame = tk.LabelFrame(
            self.root,
            text="坐标记录",
            font=('微软雅黑', 12, 'bold'),
            bg=self.colors['bg'],
            fg=self.colors['text'],
            padx=10,
            pady=10
        )
        record_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
        
        # 记录控制按钮
        record_control = tk.Frame(record_frame, bg=self.colors['bg'])
        record_control.pack(fill=tk.X, pady=(0, 10))
        
        record_btn = tk.Button(
            record_control,
            text="📍 记录当前坐标",
            command=self.record_coordinate,
            font=('微软雅黑', 10, 'bold'),
            bg=self.colors['secondary'],
            fg='white',
            relief=tk.RAISED,
            padx=15,
            pady=5
        )
        record_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        clear_btn = tk.Button(
            record_control,
            text="🗑️ 清空记录",
            command=self.clear_records,
            font=('微软雅黑', 10, 'bold'),
            bg=self.colors['danger'],
            fg='white',
            relief=tk.RAISED,
            padx=15,
            pady=5
        )
        clear_btn.pack(side=tk.LEFT)
        
        # 坐标列表显示
        list_label = tk.Label(
            record_frame,
            text="已记录的坐标点:",
            font=('微软雅黑', 10, 'bold'),
            bg=self.colors['bg'],
            fg=self.colors['text']
        )
        list_label.pack(anchor=tk.W, pady=(0, 5))
        
        # 使用Listbox显示坐标
        listbox_frame = tk.Frame(record_frame, bg=self.colors['bg'])
        listbox_frame.pack(fill=tk.BOTH, expand=True)
        
        self.coord_listbox = tk.Listbox(
            listbox_frame,
            font=('Consolas', 10),
            bg='white',
            selectbackground=self.colors['primary'],
            height=8
        )
        self.coord_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # 滚动条
        scrollbar = tk.Scrollbar(listbox_frame, orient=tk.VERTICAL)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        self.coord_listbox.config(yscrollcommand=scrollbar.set)
        scrollbar.config(command=self.coord_listbox.yview)
        
        # 双击删除提示
        tip_label = tk.Label(
            record_frame,
            text="💡 提示: 双击列表项可删除单个坐标",
            font=('微软雅黑', 8),
            bg=self.colors['bg'],
            fg='gray'
        )
        tip_label.pack(anchor=tk.W, pady=(5, 0))
        
        # 绑定双击事件
        self.coord_listbox.bind('<Double-Button-1>', self.delete_selected_coordinate)
    
    def create_code_generation_section(self):
        """创建代码生成区域"""
        code_frame = tk.LabelFrame(
            self.root,
            text="OpenCV代码生成",
            font=('微软雅黑', 12, 'bold'),
            bg=self.colors['bg'],
            fg=self.colors['text'],
            padx=10,
            pady=10
        )
        code_frame.pack(fill=tk.X, padx=20, pady=10)
        
        generate_btn = tk.Button(
            code_frame,
            text="🔧 生成截图代码",
            command=self.generate_opencv_code,
            font=('微软雅黑', 11, 'bold'),
            bg=self.colors['primary'],
            fg='white',
            relief=tk.RAISED,
            padx=30,
            pady=10
        )
        generate_btn.pack()
    
    def create_status_bar(self):
        """创建状态栏"""
        status_frame = tk.Frame(self.root, bg='#e0e0e0', relief=tk.SUNKEN, bd=1)
        status_frame.pack(fill=tk.X, side=tk.BOTTOM)
        
        self.status_var = tk.StringVar(value="状态: 正在追踪鼠标坐标...")
        status_label = tk.Label(
            status_frame,
            textvariable=self.status_var,
            font=('微软雅黑', 9),
            bg='#e0e0e0',
            fg=self.colors['text'],
            anchor=tk.W
        )
        status_label.pack(side=tk.LEFT, padx=10, pady=2)
        
        # 快捷键提示
        shortcut_label = tk.Label(
            status_frame,
            text="快捷键: [空格]记录 [C]清空 [T]追踪 [P]置顶 [L]加载图片",
            font=('微软雅黑', 8),
            bg='#e0e0e0',
            fg='gray',
            anchor=tk.E
        )
        shortcut_label.pack(side=tk.RIGHT, padx=10, pady=2)
    
    def bind_keyboard_events(self):
        """绑定键盘事件"""
        self.root.bind('<KeyPress-space>', lambda e: self.record_coordinate())
        self.root.bind('<KeyPress-c>', lambda e: self.clear_records())
        self.root.bind('<KeyPress-t>', lambda e: self.toggle_tracking())
        self.root.bind('<KeyPress-p>', lambda e: self.toggle_topmost())
        self.root.bind('<KeyPress-l>', lambda e: self.load_image())
        
        # 确保窗口可以接收键盘事件
        self.root.focus_set()
    
    def start_coordinate_tracking(self):
        """启动坐标追踪线程"""
        def track_mouse():
            while True:
                if self.is_tracking:
                    try:
                        x, y = pyautogui.position()
                        self.coord_var.set(f"X: {x}, Y: {y}")
                    except Exception as e:
                        print(f"追踪错误: {e}")
                time.sleep(0.05)  # 提高刷新率
        
        tracking_thread = threading.Thread(target=track_mouse, daemon=True)
        tracking_thread.start()
    
    def toggle_tracking(self):
        """切换追踪状态"""
        self.is_tracking = not self.is_tracking
        
        if self.is_tracking:
            self.tracking_btn.config(
                text="⏸️ 停止追踪", 
                bg=self.colors['danger']
            )
            self.status_var.set("状态: 正在追踪鼠标坐标...")
        else:
            self.tracking_btn.config(
                text="▶️ 开始追踪", 
                bg=self.colors['secondary']
            )
            self.status_var.set("状态: 已停止追踪")
    
    def toggle_topmost(self):
        """切换窗口置顶状态"""
        self.is_topmost = not self.is_topmost
        self.root.attributes('-topmost', self.is_topmost)
        
        if self.is_topmost:
            self.topmost_btn.config(text="📌 取消置顶")
        else:
            self.topmost_btn.config(text="📌 设为置顶")
    
    def record_coordinate(self):
        """记录当前坐标"""
        try:
            x, y = pyautogui.position()
            timestamp = datetime.now().strftime("%H:%M:%S")
            
            coord_data = {
                'x': x,
                'y': y,
                'time': timestamp,
                'index': len(self.coordinate_list) + 1
            }
            
            self.coordinate_list.append(coord_data)
            
            # 添加到列表框
            display_text = f"{coord_data['index']:2d}. ({x:4d}, {y:4d}) - {timestamp}"
            self.coord_listbox.insert(tk.END, display_text)
            
            # 滚动到最新项
            self.coord_listbox.see(tk.END)
            
            # 更新状态
            count = len(self.coordinate_list)
            self.status_var.set(f"状态: 已记录坐标 ({x}, {y}) - 共 {count} 个点")
            
        except Exception as e:
            messagebox.showerror("错误", f"记录坐标失败: {e}")
    
    def delete_selected_coordinate(self, event):
        """删除选中的坐标"""
        selection = self.coord_listbox.curselection()
        if selection:
            index = selection[0]
            self.coord_listbox.delete(index)
            del self.coordinate_list[index]
            
            # 重新编号
            self.refresh_coordinate_list()
    
    def refresh_coordinate_list(self):
        """刷新坐标列表显示"""
        self.coord_listbox.delete(0, tk.END)
        
        for i, coord in enumerate(self.coordinate_list):
            coord['index'] = i + 1
            display_text = f"{coord['index']:2d}. ({coord['x']:4d}, {coord['y']:4d}) - {coord['time']}"
            self.coord_listbox.insert(tk.END, display_text)
    
    def clear_records(self):
        """清空所有记录"""
        if self.coordinate_list:
            result = messagebox.askyesno("确认", "确定要清空所有坐标记录吗？")
            if result:
                self.coordinate_list.clear()
                self.coord_listbox.delete(0, tk.END)
                self.status_var.set("状态: 已清空所有记录")
    
    def load_image(self):
        """加载图片"""
        file_types = [
            ("图片文件", "*.jpg;*.jpeg;*.png;*.bmp;*.gif;*.tiff"),
            ("JPEG文件", "*.jpg;*.jpeg"),
            ("PNG文件", "*.png"),
            ("所有文件", "*.*")
        ]
        
        file_path = filedialog.askopenfilename(
            title="选择图片文件",
            filetypes=file_types
        )
        
        if file_path:
            try:
                # 加载图片
                self.image_path = file_path
                self.current_image = Image.open(file_path)
                
                # 获取图片信息
                width, height = self.current_image.size
                file_size = os.path.getsize(file_path) / 1024  # KB
                
                # 更新信息显示
                filename = os.path.basename(file_path)
                self.image_info_var.set(f"{filename} ({width}×{height}, {file_size:.1f}KB)")
                
                # 显示图片
                self.display_image()
                
                # 清空之前的图片坐标
                self.image_coordinates.clear()
                self.update_image_coordinates_display()
                
                self.status_var.set(f"状态: 已加载图片 {filename}")
                
            except Exception as e:
                messagebox.showerror("错误", f"加载图片失败: {e}")
    
    def display_image(self):
        """在画布上显示图片"""
        if not self.current_image:
            return
        
        # 清空画布
        self.image_canvas.delete("all")
        
        # 计算显示尺寸
        canvas_width = 400
        canvas_height = 300
        img_width, img_height = self.current_image.size
        
        # 计算缩放比例
        scale_x = canvas_width / img_width
        scale_y = canvas_height / img_height
        self.canvas_scale = min(scale_x, scale_y, 1.0)  # 不放大，只缩小
        
        # 缩放图片
        display_width = int(img_width * self.canvas_scale)
        display_height = int(img_height * self.canvas_scale)
        
        display_image = self.current_image.resize((display_width, display_height), Image.Resampling.LANCZOS)
        self.photo = ImageTk.PhotoImage(display_image)
        
        # 计算居中位置
        self.canvas_offset_x = (canvas_width - display_width) // 2
        self.canvas_offset_y = (canvas_height - display_height) // 2
        
        # 显示图片
        self.image_canvas.create_image(
            self.canvas_offset_x, self.canvas_offset_y, 
            anchor=tk.NW, image=self.photo, tags="image"
        )
        
        # 更新滚动区域
        self.image_canvas.configure(scrollregion=self.image_canvas.bbox("all"))
        
        # 重绘已有的坐标点
        self.redraw_image_coordinates()
    
    def on_image_click(self, event):
        """处理图片点击事件"""
        if not self.current_image:
            return
        
        # 获取点击位置
        canvas_x = self.image_canvas.canvasx(event.x)
        canvas_y = self.image_canvas.canvasy(event.y)
        
        # 转换为图片坐标
        img_x = int((canvas_x - self.canvas_offset_x) / self.canvas_scale)
        img_y = int((canvas_y - self.canvas_offset_y) / self.canvas_scale)
        
        # 检查点击是否在图片范围内
        img_width, img_height = self.current_image.size
        if 0 <= img_x < img_width and 0 <= img_y < img_height:
            # 记录坐标
            timestamp = datetime.now().strftime("%H:%M:%S")
            coord_data = {
                'x': img_x,
                'y': img_y,
                'time': timestamp,
                'canvas_x': canvas_x,
                'canvas_y': canvas_y
            }
            
            self.image_coordinates.append(coord_data)
            
            # 在画布上绘制标记
            self.draw_coordinate_marker(canvas_x, canvas_y, len(self.image_coordinates))
            
            # 更新显示
            self.update_image_coordinates_display()
            
            self.status_var.set(f"状态: 在图片上记录坐标 ({img_x}, {img_y})")
    
    def draw_coordinate_marker(self, canvas_x, canvas_y, index):
        """在画布上绘制坐标标记"""
        colors = ['red', 'green', 'blue', 'orange', 'purple', 'brown']
        color = colors[(index - 1) % len(colors)]
        
        # 绘制圆形标记
        r = 5
        self.image_canvas.create_oval(
            canvas_x - r, canvas_y - r, canvas_x + r, canvas_y + r,
            fill=color, outline='white', width=2, tags="marker"
        )
        
        # 绘制编号
        self.image_canvas.create_text(
            canvas_x + 15, canvas_y - 15,
            text=str(index), fill=color, font=('Arial', 10, 'bold'),
            tags="marker"
        )
    
    def redraw_image_coordinates(self):
        """重新绘制所有坐标标记"""
        # 清除旧标记
        self.image_canvas.delete("marker")
        
        # 重绘所有标记
        for i, coord in enumerate(self.image_coordinates):
            # 重新计算画布坐标
            canvas_x = coord['x'] * self.canvas_scale + self.canvas_offset_x
            canvas_y = coord['y'] * self.canvas_scale + self.canvas_offset_y
            
            # 更新画布坐标
            coord['canvas_x'] = canvas_x
            coord['canvas_y'] = canvas_y
            
            # 绘制标记
            self.draw_coordinate_marker(canvas_x, canvas_y, i + 1)
    
    def update_image_coordinates_display(self):
        """更新图片坐标显示"""
        if not self.image_coordinates:
            self.image_coords_var.set("未选择")
        else:
            count = len(self.image_coordinates)
            last_coord = self.image_coordinates[-1]
            self.image_coords_var.set(
                f"共{count}个点，最新: ({last_coord['x']}, {last_coord['y']})"
            )
    
    def clear_image_coordinates(self):
        """清空图片坐标"""
        if self.image_coordinates:
            result = messagebox.askyesno("确认", "确定要清空图片上的所有坐标点吗？")
            if result:
                self.image_coordinates.clear()
                self.image_canvas.delete("marker")
                self.update_image_coordinates_display()
                self.status_var.set("状态: 已清空图片坐标")
    
    def on_canvas_scroll(self, event):
        """处理画布滚轮事件"""
        # 这里可以添加缩放功能
        pass
    
    def generate_opencv_code(self):
        """生成OpenCV代码"""
        if not self.coordinate_list:
            messagebox.showwarning("警告", "请先记录至少一个坐标点！")
            return
        
        # 创建代码显示窗口
        self.show_code_window()
    
    def show_code_window(self):
        """显示代码窗口"""
        code_window = tk.Toplevel(self.root)
        code_window.title("生成的OpenCV代码")
        code_window.geometry("800x600")
        code_window.configure(bg=self.colors['bg'])
        
        # 代码类型选择
        type_frame = tk.Frame(code_window, bg=self.colors['bg'])
        type_frame.pack(fill=tk.X, padx=20, pady=10)
        
        type_label = tk.Label(
            type_frame,
            text="代码类型:",
            font=('微软雅黑', 12, 'bold'),
            bg=self.colors['bg']
        )
        type_label.pack(side=tk.LEFT)
        
        # 根据坐标数量自动选择合适的代码类型
        screen_coord_count = len(self.coordinate_list)
        image_coord_count = len(self.image_coordinates)
        
        if image_coord_count > 0:
            if image_coord_count == 1:
                default_type = "image_single"
            elif image_coord_count == 2:
                default_type = "image_rectangle"
            else:
                default_type = "image_multiple"
        elif screen_coord_count == 1:
            default_type = "single"
        elif screen_coord_count == 2:
            default_type = "rectangle"
        else:
            default_type = "multiple"
        
        self.code_type = tk.StringVar(value=default_type)
        
        type_options = [
            ("屏幕单点标记", "single"),
            ("屏幕矩形截图", "rectangle"),
            ("屏幕多点标记", "multiple"),
            ("图片单点标记", "image_single"),
            ("图片矩形截图", "image_rectangle"),
            ("图片多点标记", "image_multiple")
        ]
        
        for text, value in type_options:
            rb = tk.Radiobutton(
                type_frame,
                text=text,
                variable=self.code_type,
                value=value,
                command=lambda: self.update_code_display(code_text),
                font=('微软雅黑', 10),
                bg=self.colors['bg']
            )
            rb.pack(side=tk.LEFT, padx=20)
        
        # 代码显示区域
        code_text = scrolledtext.ScrolledText(
            code_window,
            font=('Consolas', 10),
            bg='#2d2d2d',
            fg='#f8f8f2',
            insertbackground='white',
            wrap=tk.WORD
        )
        code_text.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
        
        # 按钮区域
        button_frame = tk.Frame(code_window, bg=self.colors['bg'])
        button_frame.pack(fill=tk.X, padx=20, pady=10)
        
        copy_btn = tk.Button(
            button_frame,
            text="📋 复制代码",
            command=lambda: self.copy_code_to_clipboard(code_text.get(1.0, tk.END)),
            font=('微软雅黑', 10, 'bold'),
            bg=self.colors['secondary'],
            fg='white',
            padx=20,
            pady=5
        )
        copy_btn.pack(side=tk.LEFT)
        
        save_btn = tk.Button(
            button_frame,
            text="💾 保存代码",
            command=lambda: self.save_code_to_file(code_text.get(1.0, tk.END)),
            font=('微软雅黑', 10, 'bold'),
            bg=self.colors['primary'],
            fg='white',
            padx=20,
            pady=5
        )
        save_btn.pack(side=tk.LEFT, padx=10)
        
        # 初始化代码显示
        self.update_code_display(code_text)
    
    def update_code_display(self, code_text):
        """更新代码显示"""
        code_text.delete(1.0, tk.END)
        generated_code = self.create_opencv_code()
        code_text.insert(1.0, generated_code)
    
    def create_opencv_code(self):
        """生成OpenCV代码"""
        code_type = self.code_type.get()
        screen_coords = self.coordinate_list
        image_coords = self.image_coordinates
        
        if code_type == "single" and screen_coords:
            return self.generate_single_point_code(screen_coords[0])
        elif code_type == "rectangle" and len(screen_coords) >= 2:
            return self.generate_rectangle_code(screen_coords[:2])
        elif code_type == "multiple" and screen_coords:
            return self.generate_multiple_points_code(screen_coords)
        elif code_type == "image_single" and image_coords:
            return self.generate_image_single_point_code(image_coords[0])
        elif code_type == "image_rectangle" and len(image_coords) >= 2:
            return self.generate_image_rectangle_code(image_coords[:2])
        elif code_type == "image_multiple" and image_coords:
            return self.generate_image_multiple_points_code(image_coords)
        else:
            return "# 请先记录相应数量的坐标点"
    
    def generate_single_point_code(self, coord):
        """生成单点标记代码"""
        x, y = coord['x'], coord['y']
        return f'''# 单点坐标标记代码
# 记录时间: {coord['time']}
import cv2
import numpy as np
from PIL import ImageGrab

# 目标坐标点
target_point = ({x}, {y})

# 截取屏幕
screenshot = ImageGrab.grab()
screenshot = np.array(screenshot)
screenshot = cv2.cvtColor(screenshot, cv2.COLOR_RGB2BGR)

# 在目标点绘制标记
cv2.circle(screenshot, target_point, 8, (0, 0, 255), -1)  # 红色实心圆
cv2.circle(screenshot, target_point, 15, (0, 255, 0), 2)  # 绿色圆环
cv2.putText(screenshot, f'({x}, {y})', (x + 20, y - 20), 
           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)

# 显示结果
cv2.imshow('Point Marker', screenshot)
cv2.waitKey(0)
cv2.destroyAllWindows()

# 保存截图
# cv2.imwrite('point_marker.png', screenshot)
'''
    
    def generate_rectangle_code(self, coords):
        """生成矩形截图代码"""
        x1, y1 = coords[0]['x'], coords[0]['y']
        x2, y2 = coords[1]['x'], coords[1]['y']
        
        left = min(x1, x2)
        top = min(y1, y2)
        right = max(x1, x2)
        bottom = max(y1, y2)
        width = right - left
        height = bottom - top
        
        return f'''# 矩形区域截图代码
# 点1记录时间: {coords[0]['time']}
# 点2记录时间: {coords[1]['time']}
import cv2
import numpy as np
from PIL import ImageGrab

# 原始坐标点
point1 = ({x1}, {y1})
point2 = ({x2}, {y2})

# 计算矩形边界
left, top = {left}, {top}
right, bottom = {right}, {bottom}
width, height = {width}, {height}

print(f"截图区域: 左上角({left}, {top}), 右下角({right}, {bottom})")
print(f"区域大小: {width} × {height} 像素")

# 方法1: 截取指定区域
cropped_screenshot = ImageGrab.grab(bbox=(left, top, right, bottom))
cropped_array = np.array(cropped_screenshot)
cropped_array = cv2.cvtColor(cropped_array, cv2.COLOR_RGB2BGR)

# 显示截取的区域
cv2.imshow('Cropped Region', cropped_array)

# 方法2: 全屏截图并标记区域
full_screenshot = ImageGrab.grab()
full_array = np.array(full_screenshot)
full_array = cv2.cvtColor(full_array, cv2.COLOR_RGB2BGR)

# 绘制矩形框
cv2.rectangle(full_array, (left, top), (right, bottom), (0, 255, 0), 3)
cv2.putText(full_array, f'{width}x{height}', (left, top-10), 
           cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 0), 2)

# 显示带标记的全屏截图
cv2.imshow('Full Screenshot with Marker', full_array)

cv2.waitKey(0)
cv2.destroyAllWindows()

# 保存截图
# cv2.imwrite('cropped_region.png', cropped_array)
# cv2.imwrite('marked_screenshot.png', full_array)
'''
    
    def generate_multiple_points_code(self, coords):
        """生成多点标记代码"""
        coords_list = [(coord['x'], coord['y']) for coord in coords]
        
        return f'''# 多点坐标标记代码
# 共记录 {len(coords)} 个坐标点
import cv2
import numpy as np
from PIL import ImageGrab

# 所有坐标点
coordinates = {coords_list}

# 截取屏幕
screenshot = ImageGrab.grab()
screenshot = np.array(screenshot)
screenshot = cv2.cvtColor(screenshot, cv2.COLOR_RGB2BGR)

# 定义颜色列表 (BGR格式)
colors = [
    (0, 0, 255),    # 红色
    (0, 255, 0),    # 绿色
    (255, 0, 0),    # 蓝色
    (0, 255, 255),  # 黄色
    (255, 0, 255),  # 紫色
    (255, 255, 0),  # 青色
]

# 标记每个坐标点
for i, (x, y) in enumerate(coordinates):
    color = colors[i % len(colors)]
    
    # 绘制标记点
    cv2.circle(screenshot, (x, y), 8, color, -1)
    cv2.circle(screenshot, (x, y), 15, (255, 255, 255), 2)
    
    # 添加标号
    cv2.putText(screenshot, f'{i+1}', (x-8, y+5), 
               cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
    
    # 添加坐标信息
    cv2.putText(screenshot, f'({x},{y})', (x+20, y-20), 
               cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2)

# 连接所有点形成路径
if len(coordinates) > 1:
    pts = np.array(coordinates, np.int32)
    cv2.polylines(screenshot, [pts], False, (255, 255, 255), 2)

# 计算并绘制边界框
if len(coordinates) >= 2:
    x_coords = [pt[0] for pt in coordinates]
    y_coords = [pt[1] for pt in coordinates]
    bbox_left, bbox_right = min(x_coords), max(x_coords)
    bbox_top, bbox_bottom = min(y_coords), max(y_coords)
    
    cv2.rectangle(screenshot, (bbox_left, bbox_top), 
                 (bbox_right, bbox_bottom), (0, 255, 255), 2)

# 显示结果
cv2.imshow('Multiple Points Marker', screenshot)
cv2.waitKey(0)
cv2.destroyAllWindows()

# 如需截取边界框区域
if len(coordinates) >= 2:
    bbox_cropped = ImageGrab.grab(bbox=(bbox_left, bbox_top, bbox_right, bbox_bottom))
    bbox_array = np.array(bbox_cropped)
    bbox_array = cv2.cvtColor(bbox_array, cv2.COLOR_RGB2BGR)
    
    cv2.imshow('Bounding Box Region', bbox_array)
    cv2.waitKey(0)
    cv2.destroyAllWindows()

# 保存截图
# cv2.imwrite('multiple_points.png', screenshot)
'''
    
    def generate_image_single_point_code(self, coord):
        """生成图片单点标记代码"""
        x, y = coord['x'], coord['y']
        return f'''# 图片单点坐标标记代码
# 图片路径: {self.image_path}
# 记录时间: {coord['time']}
import cv2
import numpy as np

# 加载图片
image_path = r"{self.image_path}"
image = cv2.imread(image_path)

if image is None:
    print(f"无法加载图片: {{image_path}}")
    exit()

# 目标坐标点
target_point = ({x}, {y})

# 在目标点绘制标记
cv2.circle(image, target_point, 8, (0, 0, 255), -1)  # 红色实心圆
cv2.circle(image, target_point, 15, (0, 255, 0), 2)  # 绿色圆环
cv2.putText(image, f'({x}, {y})', (x + 20, y - 20), 
           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)

# 显示结果
cv2.imshow('Image Point Marker', image)
cv2.waitKey(0)
cv2.destroyAllWindows()

# 保存标记后的图片
# cv2.imwrite('marked_image.png', image)
'''
    
    def generate_image_rectangle_code(self, coords):
        """生成图片矩形区域代码"""
        x1, y1 = coords[0]['x'], coords[0]['y']
        x2, y2 = coords[1]['x'], coords[1]['y']
        
        left = min(x1, x2)
        top = min(y1, y2)
        right = max(x1, x2)
        bottom = max(y1, y2)
        width = right - left
        height = bottom - top
        
        return f'''# 图片矩形区域处理代码
# 图片路径: {self.image_path}
# 点1记录时间: {coords[0]['time']}
# 点2记录时间: {coords[1]['time']}
import cv2
import numpy as np

# 加载图片
image_path = r"{self.image_path}"
original_image = cv2.imread(image_path)

if original_image is None:
    print(f"无法加载图片: {{image_path}}")
    exit()

# 原始坐标点
point1 = ({x1}, {y1})
point2 = ({x2}, {y2})

# 计算矩形边界
left, top = {left}, {top}
right, bottom = {right}, {bottom}
width, height = {width}, {height}

print(f"截取区域: 左上角({left}, {top}), 右下角({right}, {bottom})")
print(f"区域大小: {width} × {height} 像素")

# 方法1: 截取指定区域
cropped_region = original_image[top:bottom, left:right]

# 显示截取的区域
cv2.imshow('Cropped Region', cropped_region)

# 方法2: 在原图上标记区域
marked_image = original_image.copy()
cv2.rectangle(marked_image, (left, top), (right, bottom), (0, 255, 0), 3)
cv2.putText(marked_image, f'{width}x{height}', (left, top-10), 
           cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 0), 2)

# 显示带标记的原图
cv2.imshow('Original with Marker', marked_image)

cv2.waitKey(0)
cv2.destroyAllWindows()

# 保存结果
# cv2.imwrite('cropped_region.png', cropped_region)
# cv2.imwrite('marked_image.png', marked_image)
'''
    
    def generate_image_multiple_points_code(self, coords):
        """生成图片多点标记代码"""
        coords_list = [(coord['x'], coord['y']) for coord in coords]
        
        return f'''# 图片多点坐标标记代码
# 图片路径: {self.image_path}
# 共记录 {len(coords)} 个坐标点
import cv2
import numpy as np

# 加载图片
image_path = r"{self.image_path}"
image = cv2.imread(image_path)

if image is None:
    print(f"无法加载图片: {{image_path}}")
    exit()

# 所有坐标点
coordinates = {coords_list}

# 定义颜色列表 (BGR格式)
colors = [
    (0, 0, 255),    # 红色
    (0, 255, 0),    # 绿色
    (255, 0, 0),    # 蓝色
    (0, 255, 255),  # 黄色
    (255, 0, 255),  # 紫色
    (255, 255, 0),  # 青色
]

# 标记每个坐标点
for i, (x, y) in enumerate(coordinates):
    color = colors[i % len(colors)]
    
    # 绘制标记点
    cv2.circle(image, (x, y), 8, color, -1)
    cv2.circle(image, (x, y), 15, (255, 255, 255), 2)
    
    # 添加标号
    cv2.putText(image, f'{i+1}', (x-8, y+5), 
               cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
    
    # 添加坐标信息
    cv2.putText(image, f'({x},{y})', (x+20, y-20), 
               cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2)

# 连接所有点形成路径
if len(coordinates) > 1:
    pts = np.array(coordinates, np.int32)
    cv2.polylines(image, [pts], False, (255, 255, 255), 2)

# 计算并绘制边界框
if len(coordinates) >= 2:
    x_coords = [pt[0] for pt in coordinates]
    y_coords = [pt[1] for pt in coordinates]
    bbox_left, bbox_right = min(x_coords), max(x_coords)
    bbox_top, bbox_bottom = min(y_coords), max(y_coords)
    
    cv2.rectangle(image, (bbox_left, bbox_top), 
                 (bbox_right, bbox_bottom), (0, 255, 255), 2)

# 显示结果
cv2.imshow('Image Multiple Points Marker', image)
cv2.waitKey(0)
cv2.destroyAllWindows()

# 如需截取边界框区域
if len(coordinates) >= 2:
    bbox_cropped = image[bbox_top:bbox_bottom, bbox_left:bbox_right]
    cv2.imshow('Bounding Box Region', bbox_cropped)
    cv2.waitKey(0)
    cv2.destroyAllWindows()

# 保存标记后的图片
# cv2.imwrite('image_multiple_points.png', image)
'''
    
    def copy_code_to_clipboard(self, code):
        """复制代码到剪贴板"""
        self.root.clipboard_clear()
        self.root.clipboard_append(code)
        self.root.update()
        messagebox.showinfo("成功", "代码已复制到剪贴板！")
    
    def save_code_to_file(self, code):
        """保存代码到文件"""
        from tkinter import filedialog
        
        filename = filedialog.asksaveasfilename(
            defaultextension=".py",
            filetypes=[("Python文件", "*.py"), ("所有文件", "*.*")],
            title="保存OpenCV代码"
        )
        
        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(code)
                messagebox.showinfo("成功", f"代码已保存到: {filename}")
            except Exception as e:
                messagebox.showerror("错误", f"保存失败: {e}")
    
    def run(self):
        """运行主程序"""
        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            print("程序被用户中断")
        except Exception as e:
            messagebox.showerror("错误", f"程序运行出错: {e}")

def main():
    """主函数"""
    try:
        app = MouseCoordinateTracker()
        app.run()
    except ImportError as e:
        print("缺少必要的库，请安装：")
        print("pip install pyautogui pillow")
        print(f"错误详情: {e}")
    except Exception as e:
        print(f"程序启动失败: {e}")

if __name__ == "__main__":
    main()