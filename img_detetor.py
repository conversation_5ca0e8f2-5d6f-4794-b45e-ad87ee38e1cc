import cv2
import numpy as np
from typing import List, Tuple, Optional
import matplotlib.pyplot as plt
from matplotlib.patches import Rectangle
import pandas as pd

class IconSearcher:
    def __init__(self):
        """初始化图标搜索器"""
        self.main_image = None
        self.template_image = None
        
    def load_images(self, main_image_path: str, template_path: str):
        """
        加载主图片和模板图标
        
        Args:
            main_image_path: 主图片路径
            template_path: 模板图标路径
        """
        self.main_image = cv2.imread(main_image_path)
        self.template_image = cv2.imread(template_path)
        
        if self.main_image is None:
            raise ValueError(f"无法加载主图片: {main_image_path}")
        if self.template_image is None:
            raise ValueError(f"无法加载模板图片: {template_path}")
    
    def template_matching(self, threshold: float = 0.8, method=cv2.TM_CCOEFF_NORMED) -> List[Tuple[int, int, int, int, float]]:
        """
        使用模板匹配方法搜索图标

        Args:
            threshold: 匹配阈值，0-1之间
            method: 匹配方法

        Returns:
            List of (x, y, width, height, confidence) tuples
        """
        if self.main_image is None or self.template_image is None:
            raise ValueError("请先加载图片")

        # 转换为灰度图
        main_gray = cv2.cvtColor(self.main_image, cv2.COLOR_BGR2GRAY)
        template_gray = cv2.cvtColor(self.template_image, cv2.COLOR_BGR2GRAY)

        # 获取模板尺寸
        h, w = template_gray.shape

        # 执行模板匹配
        result = cv2.matchTemplate(main_gray, template_gray, method)

        # 查找匹配位置
        locations = np.where(result >= threshold)
        matches = []

        # 非最大值抑制，避免重复检测
        for pt in zip(*locations[::-1]):
            x, y = pt
            confidence = result[y, x]

            # 检查是否与已有匹配重叠
            is_duplicate = False
            for existing_match in matches:
                ex, ey, ew, eh, _ = existing_match
                if (abs(x - ex) < w * 0.5 and abs(y - ey) < h * 0.5):
                    is_duplicate = True
                    break

            if not is_duplicate:
                matches.append((x, y, w, h, confidence))

        return matches

    def color_template_matching(self, threshold: float = 0.8, color_threshold: float = 0.8,
                               method=cv2.TM_CCOEFF_NORMED) -> List[Tuple[int, int, int, int, float]]:
        """
        使用颜色+形状模板匹配方法搜索图标

        Args:
            threshold: 形状匹配阈值，0-1之间
            color_threshold: 颜色匹配阈值，0-1之间
            method: 匹配方法

        Returns:
            List of (x, y, width, height, confidence) tuples
        """
        if self.main_image is None or self.template_image is None:
            raise ValueError("请先加载图片")

        # 获取模板尺寸
        h, w = self.template_image.shape[:2]

        # 1. 先进行形状匹配（灰度图）
        main_gray = cv2.cvtColor(self.main_image, cv2.COLOR_BGR2GRAY)
        template_gray = cv2.cvtColor(self.template_image, cv2.COLOR_BGR2GRAY)

        # 执行形状模板匹配
        shape_result = cv2.matchTemplate(main_gray, template_gray, method)

        # 2. 进行颜色匹配（每个颜色通道）
        color_results = []
        for channel in range(3):  # BGR三个通道
            main_channel = self.main_image[:, :, channel]
            template_channel = self.template_image[:, :, channel]
            color_result = cv2.matchTemplate(main_channel, template_channel, method)
            color_results.append(color_result)

        # 3. 综合形状和颜色匹配结果
        # 计算颜色匹配的平均值
        color_result_avg = np.mean(color_results, axis=0)

        # 综合置信度：形状匹配 * 颜色匹配
        combined_result = shape_result * color_result_avg

        # 查找同时满足形状和颜色阈值的匹配位置
        shape_locations = np.where(shape_result >= threshold)
        color_locations = np.where(color_result_avg >= color_threshold)

        matches = []

        # 找到同时满足形状和颜色条件的位置
        for pt in zip(*shape_locations[::-1]):
            x, y = pt

            # 检查该位置是否也满足颜色匹配条件
            if color_result_avg[y, x] >= color_threshold:
                shape_conf = shape_result[y, x]
                color_conf = color_result_avg[y, x]
                combined_conf = combined_result[y, x]

                # 检查是否与已有匹配重叠
                is_duplicate = False
                for existing_match in matches:
                    ex, ey, ew, eh, _ = existing_match
                    if (abs(x - ex) < w * 0.5 and abs(y - ey) < h * 0.5):
                        is_duplicate = True
                        break

                if not is_duplicate:
                    matches.append((x, y, w, h, combined_conf))

        return matches

    def calculate_color_similarity(self, region1: np.ndarray, region2: np.ndarray) -> float:
        """
        计算两个图像区域的颜色相似度

        Args:
            region1: 第一个图像区域
            region2: 第二个图像区域

        Returns:
            颜色相似度分数 (0-1之间，1表示完全相似)
        """
        if region1.shape != region2.shape:
            return 0.0

        # 计算每个颜色通道的直方图
        similarity_scores = []

        for channel in range(3):  # BGR三个通道
            hist1 = cv2.calcHist([region1], [channel], None, [256], [0, 256])
            hist2 = cv2.calcHist([region2], [channel], None, [256], [0, 256])

            # 使用相关系数计算相似度
            correlation = cv2.compareHist(hist1, hist2, cv2.HISTCMP_CORREL)
            similarity_scores.append(max(0, correlation))  # 确保非负

        # 返回三个通道的平均相似度
        return np.mean(similarity_scores)

    def multi_scale_matching(self, scales: List[float] = None, threshold: float = 0.8) -> List[Tuple[int, int, int, int, float]]:
        """
        多尺度模板匹配

        Args:
            scales: 缩放比例列表
            threshold: 匹配阈值

        Returns:
            List of (x, y, width, height, confidence) tuples
        """
        if scales is None:
            scales = [0.5, 0.75, 1.0, 1.25, 1.5]

        all_matches = []

        for scale in scales:
            # 缩放模板
            scaled_template = cv2.resize(self.template_image, None, fx=scale, fy=scale)

            # 临时保存原模板
            original_template = self.template_image
            self.template_image = scaled_template

            # 执行匹配
            matches = self.template_matching(threshold=threshold)
            all_matches.extend(matches)

            # 恢复原模板
            self.template_image = original_template

        # 非最大值抑制
        return self._non_max_suppression(all_matches)

    def multi_scale_color_matching(self, scales: List[float] = None, threshold: float = 0.8,
                                  color_threshold: float = 0.8) -> List[Tuple[int, int, int, int, float]]:
        """
        多尺度颜色+形状模板匹配

        Args:
            scales: 缩放比例列表
            threshold: 形状匹配阈值
            color_threshold: 颜色匹配阈值

        Returns:
            List of (x, y, width, height, confidence) tuples
        """
        if scales is None:
            scales = [0.5, 0.75, 1.0, 1.25, 1.5]

        all_matches = []

        for scale in scales:
            # 缩放模板
            scaled_template = cv2.resize(self.template_image, None, fx=scale, fy=scale)

            # 临时保存原模板
            original_template = self.template_image
            self.template_image = scaled_template

            # 执行颜色匹配
            matches = self.color_template_matching(threshold=threshold, color_threshold=color_threshold)
            all_matches.extend(matches)

            # 恢复原模板
            self.template_image = original_template

        # 非最大值抑制
        return self._non_max_suppression(all_matches)
    
    def _non_max_suppression(self, matches: List[Tuple], overlap_threshold: float = 0.3) -> List[Tuple]:
        """非最大值抑制"""
        if not matches:
            return []
        
        # 按置信度排序
        matches = sorted(matches, key=lambda x: x[4], reverse=True)
        
        filtered_matches = []
        for match in matches:
            x, y, w, h, conf = match
            
            is_overlapped = False
            for filtered_match in filtered_matches:
                fx, fy, fw, fh, _ = filtered_match
                
                # 计算重叠区域
                overlap_x = max(0, min(x + w, fx + fw) - max(x, fx))
                overlap_y = max(0, min(y + h, fy + fh) - max(y, fy))
                overlap_area = overlap_x * overlap_y
                
                # 计算重叠比例
                area1 = w * h
                area2 = fw * fh
                overlap_ratio = overlap_area / min(area1, area2)
                
                if overlap_ratio > overlap_threshold:
                    is_overlapped = True
                    break
            
            if not is_overlapped:
                filtered_matches.append(match)
        
        return filtered_matches
    
    def feature_matching(self, min_match_count: int = 10) -> List[Tuple[int, int, int, int, float]]:
        """
        使用特征匹配方法搜索图标
        
        Args:
            min_match_count: 最小匹配特征点数量
            
        Returns:
            List of (x, y, width, height, confidence) tuples
        """
        if self.main_image is None or self.template_image is None:
            raise ValueError("请先加载图片")
        
        # 转换为灰度图
        main_gray = cv2.cvtColor(self.main_image, cv2.COLOR_BGR2GRAY)
        template_gray = cv2.cvtColor(self.template_image, cv2.COLOR_BGR2GRAY)
        
        # 初始化ORB检测器
        orb = cv2.ORB_create()
        
        # 检测关键点和描述符
        kp1, des1 = orb.detectAndCompute(template_gray, None)
        kp2, des2 = orb.detectAndCompute(main_gray, None)
        
        if des1 is None or des2 is None:
            return []
        
        # 使用FLANN匹配器
        FLANN_INDEX_LSH = 6
        index_params = dict(algorithm=FLANN_INDEX_LSH,
                           table_number=6,
                           key_size=12,
                           multi_probe_level=1)
        search_params = dict(checks=50)
        
        flann = cv2.FlannBasedMatcher(index_params, search_params)
        matches = flann.knnMatch(des1, des2, k=2)
        
        # 应用Lowe's比率测试
        good_matches = []
        for match_pair in matches:
            if len(match_pair) == 2:
                m, n = match_pair
                if m.distance < 0.7 * n.distance:
                    good_matches.append(m)
        
        if len(good_matches) >= min_match_count:
            # 获取匹配点坐标
            src_pts = np.float32([kp1[m.queryIdx].pt for m in good_matches]).reshape(-1, 1, 2)
            dst_pts = np.float32([kp2[m.trainIdx].pt for m in good_matches]).reshape(-1, 1, 2)
            
            # 计算单应性矩阵
            M, mask = cv2.findHomography(src_pts, dst_pts, cv2.RANSAC, 5.0)
            
            if M is not None:
                h, w = template_gray.shape
                pts = np.float32([[0, 0], [w, 0], [w, h], [0, h]]).reshape(-1, 1, 2)
                dst = cv2.perspectiveTransform(pts, M)
                
                # 计算边界框
                x_coords = dst[:, 0, 0]
                y_coords = dst[:, 0, 1]
                x, y = int(min(x_coords)), int(min(y_coords))
                width = int(max(x_coords) - min(x_coords))
                height = int(max(y_coords) - min(y_coords))
                
                confidence = len(good_matches) / len(kp1) if len(kp1) > 0 else 0
                
                return [(x, y, width, height, confidence)]
        
        return []
    
    def search_icon(self, main_image_path: str, template_path: str,
                   method: str = "template", threshold: float = 0.8,
                   color_threshold: float = 0.8) -> List[Tuple[int, int, int, int, float]]:
        """
        搜索图标的主要接口

        Args:
            main_image_path: 主图片路径
            template_path: 模板图标路径
            method: 搜索方法 ("template", "multi_scale", "feature", "color", "multi_scale_color")
            threshold: 形状匹配阈值
            color_threshold: 颜色匹配阈值（仅用于颜色匹配方法）

        Returns:
            List of (x, y, width, height, confidence) tuples
        """
        self.load_images(main_image_path, template_path)

        if method == "template":
            return self.template_matching(threshold=threshold)
        elif method == "multi_scale":
            return self.multi_scale_matching(threshold=threshold)
        elif method == "feature":
            return self.feature_matching()
        elif method == "color":
            return self.color_template_matching(threshold=threshold, color_threshold=color_threshold)
        elif method == "multi_scale_color":
            return self.multi_scale_color_matching(threshold=threshold, color_threshold=color_threshold)
        else:
            raise ValueError("不支持的方法，请选择: 'template', 'multi_scale', 'feature', 'color', 'multi_scale_color'")
    
    def visualize_results(self, matches: List[Tuple], save_path: Optional[str] = None):
        """
        可视化搜索结果
        
        Args:
            matches: 匹配结果列表
            save_path: 保存路径（可选）
        """
        if self.main_image is None:
            raise ValueError("请先加载主图片")
        
        # 转换BGR到RGB用于matplotlib显示
        image_rgb = cv2.cvtColor(self.main_image, cv2.COLOR_BGR2RGB)
        
        fig, ax = plt.subplots(1, 1, figsize=(12, 8))
        ax.imshow(image_rgb)
        
        # 绘制检测到的区域
        for i, (x, y, w, h, conf) in enumerate(matches):
            rect = Rectangle((x, y), w, h, linewidth=2, edgecolor='red', facecolor='none')
            ax.add_patch(rect)
            ax.text(x, y-10, f'Match {i+1}: {conf:.3f}', 
                   bbox=dict(facecolor='yellow', alpha=0.7), fontsize=8)
        
        ax.set_title(f'找到 {len(matches)} 个匹配项')
        ax.axis('off')
        
        if save_path:
            plt.savefig(save_path, bbox_inches='tight', dpi=300)
        
        plt.show()
    
    def print_results(self, matches: List[Tuple]):
        """打印搜索结果"""
        if not matches:
            print("未找到匹配的图标")
            return
        
        print(f"找到 {len(matches)} 个匹配项:")
        print("-" * 60)
        for i, (x, y, w, h, conf) in enumerate(matches, 1):
            print(f"匹配 {i}:")
            print(f"  位置: ({x}, {y})")
            print(f"  尺寸: {w} x {h}")
            print(f"  置信度: {conf:.3f}")
            print(f"  中心点: ({x + w//2}, {y + h//2})")
            print("-" * 30)

    def judge_coor_range(self, matches: List[Tuple], mode):
        """打印搜索结果"""

        if not matches:
            print("未找到匹配的图标")
            last_flag = -1
            return last_flag
        
        print(f"找到 {len(matches)} 个匹配项:")
        print("-" * 60)
        coor_list = []
        for i, (x, y, w, h, conf) in enumerate(matches, 1):
            coor_list.append(x + w//2)
        max_value = max(coor_list)
        if 710 <= max_value <= 890 and mode == "G":
            last_flag = 1
        elif 710 <= max_value <= 890 and mode == "S":
            last_flag = 0
        else:
            last_flag = -1
        return last_flag
def opertor_status_csv():
    path = r"C:/Users/<USER>/Documents/ths_gs_resource/stock_pool.csv"
    pd = pd.read_csv(path)

# 使用示例
if __name__ == "__main__":
    # 创建搜索器实例
    searcher = IconSearcher()

    # 示例用法
    try:
        print("=== 图标匹配测试 ===")
        print("测试不同的匹配方法...")

        # 方法1: 基本模板匹配（仅形状）
        print("\n1. 基本模板匹配（仅形状）:")
        matches = searcher.search_icon(
            main_image_path="cropped_kline_000012_screenshot_20250811_202402.png",
            template_path="G_icon.jpg",
            method="template",
            threshold=0.75
        )
        searcher.print_results(matches)
        last_flag = searcher.judge_coor_range(matches, mode='G')
        searcher.visualize_results(matches, save_path="Gcolor_search_results.jpg")

        print("\n1. 基本模板匹配（仅形状）:")
        matches = searcher.search_icon(
            main_image_path="cropped_kline_000012_screenshot_20250811_202402.png",
            template_path="S_icon.jpg",
            method="template",
            threshold=0.75
        )
        searcher.print_results(matches)
        last_flag = searcher.judge_coor_range(matches, mode = 'S')
        searcher.visualize_results(matches, save_path="Scolor_search_results.jpg")

        # 方法2: 颜色+形状匹配
        # print("\n2. 颜色+形状匹配:")
        # matches_color = searcher.search_icon(
        #     main_image_path="cropped_kline_SZ002402_screenshot_20250810_40430.png",
        #     template_path="S_icon.jpg",
        #     method="color",
        #     threshold=0.5,
        #     color_threshold=0.5
        # )
        # searcher.print_results(matches_color)
        # searcher.visualize_results(matches_color, save_path="color_search_results.jpg")

        # 方法3: 多尺度颜色+形状匹配（推荐）
        # print("\n3. 多尺度颜色+形状匹配（推荐）:")
        # matches_multi_color = searcher.search_icon(
        #     main_image_path="cropped_kline_SZ002402_screenshot_20250810_230430.png",
        #     template_path="S_icon.jpg",
        #     method="multi_scale_color",
        #     threshold=0.6,
        #     color_threshold=0.5
        # )
        # searcher.print_results(matches_multi_color)
        # searcher.visualize_results(matches_multi_color, save_path="multi_scale_color_results.jpg")

        # # 方法4: 多尺度匹配（仅形状，用于对比）
        # print("\n4. 多尺度匹配（仅形状，用于对比）:")
        # matches_multi = searcher.search_icon(
        #     main_image_path="cropped_kline_SZ002097_screenshot_20250810_214955.png",
        #     template_path="G_icon.jpg",
        #     method="multi_scale",
        #     threshold=0.8
        # )
        # searcher.print_results(matches_multi)
        # searcher.visualize_results(matches_multi, save_path="multi_scale_color_results.jpg")

        # # 方法5: 特征匹配（适用于有旋转或变形的图标）
        # print("\n5. 特征匹配:")
        # matches_feature = searcher.search_icon(
        #     main_image_path="cropped_kline_SZ002097_screenshot_20250810_214955.png",
        #     template_path="G_icon.jpg",
        #     method="feature"
        # )
        # searcher.print_results(matches_feature)
        # searcher.visualize_results(matches_feature, save_path="multi_scale_color_results.jpg")

        # print("\n=== 匹配结果对比 ===")
        # print(f"基本模板匹配: {len(matches)} 个匹配")
        # print(f"颜色+形状匹配: {len(matches_color)} 个匹配")
        # print(f"多尺度颜色+形状匹配: {len(matches_multi_color)} 个匹配")
        # print(f"多尺度匹配（仅形状）: {len(matches_multi)} 个匹配")
        # print(f"特征匹配: {len(matches_feature)} 个匹配")

        # print("\n推荐使用 '多尺度颜色+形状匹配' 方法，它能同时考虑图标的形状和颜色，提供更准确的匹配结果。")

    except Exception as e:
        print(f"错误: {e}")
        print("\n使用说明:")
        print("1. 将要搜索的主图片保存为 'main_image.jpg'")
        print("2. 将图标模板保存为 'icon_template.jpg'")
        print("3. 运行此脚本")
        print("\n新增功能:")
        print("- 'color': 颜色+形状匹配")
        print("- 'multi_scale_color': 多尺度颜色+形状匹配（推荐）")