import pygetwindow as gw
import pyautogui
import time
from typing import List, Optional, Tuple, Dict
import re
import psutil
import win32gui
import win32con
import win32process

class WindowManager:
    def __init__(self):
        """初始化窗口管理器"""
        # 禁用pyautogui的安全检查，允许移动到屏幕边缘
        pyautogui.FAILSAFE = False
        
    def find_windows_by_title(self, title_pattern: str, exact_match: bool = False) -> List[gw.Win32Window]:
        """
        根据窗口标题查找窗口
        
        Args:
            title_pattern: 窗口标题模式（支持正则表达式）
            exact_match: 是否精确匹配
            
        Returns:
            匹配的窗口列表
        """
        all_windows = gw.getAllWindows()
        matching_windows = []
        
        for window in all_windows:
            if not window.title:  # 跳过没有标题的窗口
                continue
                
            if exact_match:
                if window.title == title_pattern:
                    matching_windows.append(window)
            else:
                if re.search(title_pattern, window.title, re.IGNORECASE):
                    matching_windows.append(window)
        
        return matching_windows
    
    def find_windows_by_process_name(self, process_name: str) -> List[gw.Win32Window]:
        """
        根据进程名查找窗口
        
        Args:
            process_name: 进程名（如 "notepad.exe"）
            
        Returns:
            匹配的窗口列表
        """
        matching_windows = []
        
        def enum_window_callback(hwnd, windows):
            if win32gui.IsWindowVisible(hwnd):
                try:
                    _, pid = win32process.GetWindowThreadProcessId(hwnd)
                    process = psutil.Process(pid)
                    if process.name().lower() == process_name.lower():
                        # 获取窗口标题
                        title = win32gui.GetWindowText(hwnd)
                        if title:  # 只添加有标题的窗口
                            # 创建pygetwindow对象
                            try:
                                window = gw.Win32Window(hwnd)
                                windows.append(window)
                            except:
                                pass
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    pass
            return True
        
        windows = []
        win32gui.EnumWindows(enum_window_callback, windows)
        return windows
    
    def get_all_windows_info(self) -> List[Dict]:
        """
        获取所有窗口的详细信息
        
        Returns:
            包含窗口信息的字典列表
        """
        windows_info = []
        all_windows = gw.getAllWindows()
        
        for window in all_windows:
            if not window.title:
                continue
                
            try:
                # 获取进程信息
                hwnd = window._hWnd
                _, pid = win32process.GetWindowThreadProcessId(hwnd)
                process = psutil.Process(pid)
                
                window_info = {
                    'title': window.title,
                    'process_name': process.name(),
                    'pid': pid,
                    'position': (window.left, window.top),
                    'size': (window.width, window.height),
                    'visible': window.visible,
                    'minimized': window.isMinimized,
                    'maximized': window.isMaximized,
                    'active': window.isActive,
                    'hwnd': hwnd
                }
                windows_info.append(window_info)
            except:
                continue
        
        return windows_info
    
    def resize_window(self, window: gw.Win32Window, width: int, height: int) -> bool:
        """
        调整窗口大小
        
        Args:
            window: 窗口对象
            width: 新宽度
            height: 新高度
            
        Returns:
            是否成功
        """
        try:
            window.resizeTo(width, height)
            return True
        except Exception as e:
            print(f"调整窗口大小失败: {e}")
            return False
    
    def move_window(self, window: gw.Win32Window, x: int, y: int) -> bool:
        """
        移动窗口位置
        
        Args:
            window: 窗口对象
            x: 新的x坐标
            y: 新的y坐标
            
        Returns:
            是否成功
        """
        try:
            window.moveTo(x, y)
            return True
        except Exception as e:
            print(f"移动窗口失败: {e}")
            return False
    
    def resize_and_move_window(self, window: gw.Win32Window, x: int, y: int, width: int, height: int) -> bool:
        """
        同时调整窗口大小和位置
        
        Args:
            window: 窗口对象
            x: 新的x坐标
            y: 新的y坐标
            width: 新宽度
            height: 新高度
            
        Returns:
            是否成功
        """
        try:
            # 如果窗口最小化，先恢复
            if window.isMinimized:
                window.restore()
                time.sleep(0.1)
            
            # 设置窗口位置和大小
            hwnd = window._hWnd
            win32gui.SetWindowPos(hwnd, win32con.HWND_TOP, x, y, width, height, 
                                win32con.SWP_SHOWWINDOW)
            return True
        except Exception as e:
            print(f"调整窗口失败: {e}")
            return False
    
    def activate_window(self, window: gw.Win32Window) -> bool:
        """
        激活窗口（置于前台）
        
        Args:
            window: 窗口对象
            
        Returns:
            是否成功
        """
        try:
            if window.isMinimized:
                window.restore()
            window.activate()
            return True
        except Exception as e:
            print(f"激活窗口失败: {e}")
            return False
    
    def minimize_window(self, window: gw.Win32Window) -> bool:
        """最小化窗口"""
        try:
            window.minimize()
            return True
        except Exception as e:
            print(f"最小化窗口失败: {e}")
            return False
    
    def maximize_window(self, window: gw.Win32Window) -> bool:
        """最大化窗口"""
        try:
            window.maximize()
            return True
        except Exception as e:
            print(f"最大化窗口失败: {e}")
            return False
    
    def close_window(self, window: gw.Win32Window) -> bool:
        """关闭窗口"""
        try:
            window.close()
            return True
        except Exception as e:
            print(f"关闭窗口失败: {e}")
            return False
    
    def get_screen_size(self) -> Tuple[int, int]:
        """获取屏幕尺寸"""
        return pyautogui.size()
    
    def center_window(self, window: gw.Win32Window, width: Optional[int] = None, height: Optional[int] = None) -> bool:
        """
        将窗口居中显示
        
        Args:
            window: 窗口对象
            width: 新宽度（可选）
            height: 新高度（可选）
            
        Returns:
            是否成功
        """
        try:
            screen_width, screen_height = self.get_screen_size()
            
            # 使用指定尺寸或当前尺寸
            w = width if width else window.width
            h = height if height else window.height
            
            # 计算居中位置
            x = (screen_width - w) // 2
            y = (screen_height - h) // 2
            
            return self.resize_and_move_window(window, x, y, w, h)
        except Exception as e:
            print(f"居中窗口失败: {e}")
            return False
    
    def arrange_windows_grid(self, windows: List[gw.Win32Window], rows: int, cols: int) -> bool:
        """
        将多个窗口按网格排列
        
        Args:
            windows: 窗口列表
            rows: 行数
            cols: 列数
            
        Returns:
            是否成功
        """
        if len(windows) > rows * cols:
            print(f"窗口数量({len(windows)})超过网格容量({rows * cols})")
            return False
        
        try:
            screen_width, screen_height = self.get_screen_size()
            
            # 计算每个窗口的尺寸
            window_width = screen_width // cols
            window_height = screen_height // rows
            
            for i, window in enumerate(windows):
                row = i // cols
                col = i % cols
                
                x = col * window_width
                y = row * window_height
                
                self.resize_and_move_window(window, x, y, window_width, window_height)
                time.sleep(0.1)  # 避免操作过快
            
            return True
        except Exception as e:
            print(f"排列窗口失败: {e}")
            return False
    
    def find_and_manage_window(self, app_identifier: str, 
                              search_type: str = "title",
                              new_width: Optional[int] = None,
                              new_height: Optional[int] = None,
                              new_x: Optional[int] = None,
                              new_y: Optional[int] = None,
                              center: bool = False,
                              activate: bool = True) -> List[gw.Win32Window]:
        """
        查找并管理窗口的综合方法
        
        Args:
            app_identifier: 应用程序标识符（标题或进程名）
            search_type: 搜索类型 ("title" 或 "process")
            new_width: 新宽度
            new_height: 新高度
            new_x: 新x坐标
            new_y: 新y坐标
            center: 是否居中
            activate: 是否激活窗口
            
        Returns:
            找到并处理的窗口列表
        """
        # 查找窗口
        if search_type == "title":
            windows = self.find_windows_by_title(app_identifier)
        elif search_type == "process":
            windows = self.find_windows_by_process_name(app_identifier)
        else:
            raise ValueError("search_type 必须是 'title' 或 'process'")
        
        if not windows:
            print(f"未找到匹配的窗口: {app_identifier}")
            return []
        
        print(f"找到 {len(windows)} 个匹配的窗口")
        
        # 处理每个窗口
        for i, window in enumerate(windows):
            print(f"处理窗口 {i+1}: {window.title}")
            
            if activate:
                self.activate_window(window)
                time.sleep(0.1)
            
            if center:
                self.center_window(window, new_width, new_height)
            elif new_x is not None and new_y is not None:
                if new_width and new_height:
                    self.resize_and_move_window(window, new_x, new_y, new_width, new_height)
                else:
                    self.move_window(window, new_x, new_y)
            elif new_width and new_height:
                self.resize_window(window, new_width, new_height)
        
        return windows
    
    def print_window_info(self, window: gw.Win32Window):
        """打印窗口详细信息"""
        try:
            hwnd = window._hWnd
            _, pid = win32process.GetWindowThreadProcessId(hwnd)
            process = psutil.Process(pid)
            
            print(f"窗口信息:")
            print(f"  标题: {window.title}")
            print(f"  进程: {process.name()} (PID: {pid})")
            print(f"  位置: ({window.left}, {window.top})")
            print(f"  尺寸: {window.width} x {window.height}")
            print(f"  可见: {window.visible}")
            print(f"  最小化: {window.isMinimized}")
            print(f"  最大化: {window.isMaximized}")
            print(f"  激活: {window.isActive}")
            print(f"  句柄: {hwnd}")
            print("-" * 40)
        except Exception as e:
            print(f"获取窗口信息失败: {e}")

# 使用示例和工具函数
def list_all_windows():
    """列出所有窗口"""
    wm = WindowManager()
    windows_info = wm.get_all_windows_info()
    
    print(f"找到 {len(windows_info)} 个窗口:")
    print("=" * 80)
    
    for i, info in enumerate(windows_info, 1):
        print(f"{i:3d}. {info['title'][:50]:<50} | {info['process_name']:<20}")
        print(f"     位置: ({info['position'][0]}, {info['position'][1]}) | "
              f"大小: {info['size'][0]}x{info['size'][1]} | "
              f"PID: {info['pid']}")
        print("-" * 80)

def demo_usage():
    """演示使用方法"""
    wm = WindowManager()
    
    print("=== 窗口管理器演示 ===\n")
    
    # 示例1: 查找记事本窗口并调整大小
    print("1. 查找记事本窗口...")
    notepad_windows = wm.find_windows_by_process_name("notepad.exe")
    if notepad_windows:
        window = notepad_windows[0]
        print(f"找到记事本: {window.title}")
        wm.resize_and_move_window(window, 100, 100, 800, 600)
        print("已调整记事本窗口大小和位置")
    else:
        print("未找到记事本窗口")
    
    print()
    
    # 示例2: 查找包含特定文字的窗口
    print("2. 查找包含'Chrome'的窗口...")
    chrome_windows = wm.find_windows_by_title("Chrome")
    for window in chrome_windows:
        print(f"找到: {window.title}")
        wm.print_window_info(window)
    
    print()
    
    # 示例3: 居中显示窗口
    if chrome_windows:
        print("3. 居中显示第一个Chrome窗口...")
        wm.center_window(chrome_windows[0], 1200, 800)

if __name__ == "__main__":
    # 创建窗口管理器
    wm = WindowManager()
    
    # 方法1: 快速查找和管理窗口
    windows = wm.find_and_manage_window(
        app_identifier="同花顺金融大师至尊整合版 - 沪市明暗盘拓赢资金分析",      # 窗口标题包含"记事本"
        search_type="title",         # 按标题搜索
        new_width=800,              # 设置宽度800像素
        new_height=600,             # 设置高度600像素
        center=True,                # 居中显示
        activate=True               # 激活窗口
    )
    
    # 方法2: 按进程名查找
    windows = wm.find_and_manage_window(
        app_identifier="hexin.exe", # 进程名
        search_type="process",       # 按进程搜索
        new_x=0,                    # 移动到左上角
        new_y=0,
        new_width=1920,             # 全屏宽度
        new_height=1080             # 全屏高度
    )
    
    # 方法3: 列出所有窗口
    list_all_windows()
    
    # 方法4: 演示功能
    # demo_usage()7