import pytesseract
from PIL import Image, ImageEnhance, ImageFilter
import cv2
import numpy as np
import os
import shutil
from typing import Optional, Union
# import paddlehub as hub
import cv2
from cnocr import CnOcr

class OCRTextRecognizer:
    """OCR文字识别类"""
    
    def __init__(self, tesseract_path: Optional[str] = None):
        """
        初始化OCR识别器

        Args:
            tesseract_path: Tesseract执行文件路径（Windows用户需要设置）
        """
        # 若未显式传入，尝试自动定位
        if not tesseract_path:
            tesseract_path = self.locate_tesseract()
        if tesseract_path:
            pytesseract.pytesseract.tesseract_cmd = tesseract_path
        # 检查Tesseract是否可用，以便在运行时给出更友好的错误提示
        self.tesseract_ready = True
        try:
            _ = pytesseract.get_tesseract_version()
        except Exception as e:
            self.tesseract_ready = False
            self._tesseract_error = str(e)

    @staticmethod
    def locate_tesseract() -> Optional[str]:
        """尝试在系统中定位Tesseract可执行文件。"""
        # 优先检查 PATH
        which_path = shutil.which('tesseract')
        if which_path:
            return which_path
        # 常见Windows安装路径
        candidates = [
            r"C:\\Program Files\\Tesseract-OCR\\tesseract.exe",
            r"C:\\Program Files (x86)\\Tesseract-OCR\\tesseract.exe",
        ]
        for p in candidates:
            if os.path.exists(p):
                return p
        return None
    
    def preprocess_image(self, image_path: str, enhance: bool = True) -> Image.Image:
        """
        图像预处理，提高OCR识别准确率
        
        Args:
            image_path: 图像文件路径
            enhance: 是否进行图像增强
            
        Returns:
            处理后的PIL图像对象
        """
        # 读取图像
        image = Image.open(image_path)
        
        if enhance:
            # 转换为灰度图
            if image.mode != 'L':
                image = image.convert('L')
            
            # 增强对比度
            enhancer = ImageEnhance.Contrast(image)
            image = enhancer.enhance(2.0)
            
            # 增强锐度
            enhancer = ImageEnhance.Sharpness(image)
            image = enhancer.enhance(1.5)
            
            # 应用滤镜减少噪声
            image = image.filter(ImageFilter.MedianFilter())
        
        return image
    
    def preprocess_with_opencv(self, image_path: str) -> np.ndarray:
        """
        使用OpenCV进行更高级的图像预处理
        
        Args:
            image_path: 图像文件路径
            
        Returns:
            处理后的图像数组
        """
        # 读取图像
        image = cv2.imread(image_path)
        if image is None:
            raise FileNotFoundError(f"无法读取图像: {image_path}")

        # 转换为灰度图
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

        # 高斯模糊去噪
        blurred = cv2.GaussianBlur(gray, (5, 5), 0)

        # 自适应阈值二值化
        thresh = cv2.adaptiveThreshold(
            blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
            cv2.THRESH_BINARY, 11, 2
        )
        
        # 形态学操作去除噪声
        kernel = np.ones((1, 1), np.uint8)
        processed = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, kernel)
        processed = cv2.morphologyEx(processed, cv2.MORPH_OPEN, kernel)
        
        return processed
    
    def recognize_text(self, 
                      image_input: Union[str, Image.Image, np.ndarray],
                      lang: str = 'chi_sim+eng',
                      config: str = '--psm 6',
                      preprocess: bool = True) -> str:
        """
        识别图像中的文字
        
        Args:
            image_input: 图像输入（文件路径、PIL图像或numpy数组）
            lang: 识别语言（chi_sim=简体中文, chi_tra=繁体中文, eng=英文）
            config: Tesseract配置参数
            preprocess: 是否进行预处理
            
        Returns:
            识别出的文字
        """
        try:
            # 处理不同类型的输入
            if isinstance(image_input, str):
                # 文件路径
                if preprocess:
                    image = self.preprocess_image(image_input)
                else:
                    image = Image.open(image_input)
            elif isinstance(image_input, np.ndarray):
                # OpenCV图像数组: 若为三通道则将BGR转换为RGB
                arr = image_input
                if getattr(arr, 'ndim', 0) == 3 and getattr(arr, 'shape', (0, 0, 0))[2] == 3:
                    try:
                        arr = cv2.cvtColor(arr, cv2.COLOR_BGR2RGB)
                    except Exception:
                        # 退化处理：直接翻转通道
                        arr = arr[:, :, ::-1]
                image = Image.fromarray(arr)
            elif isinstance(image_input, Image.Image):
                # PIL图像
                image = image_input
            else:
                raise ValueError("不支持的图像输入类型")
            
            # 执行OCR识别
            if hasattr(self, 'tesseract_ready') and not self.tesseract_ready:
                return f"OCR识别出错: 未检测到Tesseract，请安装后或在初始化时传入tesseract_path。原始错误: {getattr(self, '_tesseract_error', '')}"
            text = pytesseract.image_to_string(
                image,
                lang=lang,
                config=config
            )
            
            return text.strip()
            
        except Exception as e:
            return f"OCR识别出错: {str(e)}"
    
    def get_text_with_boxes(self, 
                           image_input: Union[str, Image.Image],
                           lang: str = 'chi_sim+eng') -> list:
        """
        获取文字及其在图像中的位置信息
        
        Args:
            image_input: 图像输入
            lang: 识别语言
            
        Returns:
            包含文字和位置信息的列表
        """
        try:
            if isinstance(image_input, str):
                image = Image.open(image_input)
            else:
                image = image_input
            
            # 获取详细的OCR结果
            data = pytesseract.image_to_data(
                image, 
                lang=lang, 
                output_type=pytesseract.Output.DICT
            )
            
            results = []
            n_boxes = len(data['text'])
            
            for i in range(n_boxes):
                conf_raw = data.get('conf', [''])[i]
                try:
                    conf_val = float(conf_raw)
                except Exception:
                    conf_val = -1.0
                text = (data.get('text', [''])[i] or '').strip()
                if conf_val > 30 and text:  # 置信度阈值 + 非空文字
                    x = int(data['left'][i]); y = int(data['top'][i])
                    w = int(data['width'][i]); h = int(data['height'][i]
                    )
                    results.append({
                        'text': text,
                        'confidence': conf_val,
                        'bbox': (x, y, x + w, y + h)
                    })
            
            return results
            
        except Exception as e:
            return [{'error': f"获取文字位置出错: {str(e)}"}]
    
    def batch_recognize(self, 
                       image_folder: str,
                       output_file: Optional[str] = None,
                       lang: str = 'chi_sim+eng') -> dict:
        """
        批量识别文件夹中的图像
        
        Args:
            image_folder: 图像文件夹路径
            output_file: 输出结果文件路径
            lang: 识别语言
            
        Returns:
            识别结果字典
        """
        results = {}
        supported_formats = ('.png', '.jpg', '.jpeg', '.bmp', '.tiff', '.tif')
        
        try:
            for filename in os.listdir(image_folder):
                if filename.lower().endswith(supported_formats):
                    image_path = os.path.join(image_folder, filename)
                    text = self.recognize_text(image_path, lang=lang)
                    results[filename] = text
                    print(f"已处理: {filename}")
            
            # 如果指定了输出文件，保存结果
            if output_file:
                with open(output_file, 'w', encoding='utf-8') as f:
                    for filename, text in results.items():
                        f.write(f"=== {filename} ===\n")
                        f.write(f"{text}\n\n")
                print(f"结果已保存到: {output_file}")
            
            return results
            
        except Exception as e:
            return {'error': f"批量处理出错: {str(e)}"}

def main():
    """使用示例"""
    # Windows用户需要设置Tesseract路径
    # tesseract_path = r'C:\Program Files\Tesseract-OCR\tesseract.exe'
    # ocr = OCRTextRecognizer(tesseract_path)
    
    # Linux/Mac用户
    ocr = OCRTextRecognizer()
    
    # 示例1: 基本文字识别
    print("=== 基本文字识别 ===")
    image_path = r"macd.jpg" # 替换为你的图像路径
    
    if os.path.exists(image_path):
        # 识别中英文混合文字
        text = ocr.recognize_text(image_path, lang='eng')
        print("识别结果:")
        print(text)
        
        # 获取文字位置信息
        print("\n=== 文字位置信息 ===")
        text_boxes = ocr.get_text_with_boxes(image_path)
        if isinstance(text_boxes, list) and text_boxes and 'error' in text_boxes[0]:
            print(text_boxes[0]['error'])
        else:
            for item in text_boxes[:5]:  # 显示前5个结果
                print(f"文字: {item['text']}, 置信度: {item['confidence']}, 位置: {item['bbox']}")
    
    else:
        print(f"图像文件 {image_path} 不存在")
        print("请将图像文件放在脚本同一目录下，或修改image_path变量")
    
    # 示例2: 使用OpenCV预处理
    print("\n=== 使用OpenCV预处理 ===")
    if os.path.exists(image_path):
        processed_image = ocr.preprocess_with_opencv(image_path)
        text = ocr.recognize_text(processed_image, lang='eng')
        print("预处理后识别结果:")
        print(text)
    
    # 示例3: 批量处理（如果有图像文件夹）
    print("\n=== 批量处理示例 ===")
    image_folder = "images"  # 图像文件夹路径
    if os.path.exists(image_folder):
        results = ocr.batch_recognize(image_folder, "ocr_results.txt")
        print(f"批量处理完成，共处理 {len(results)} 个文件")



if __name__ == "__main__":
    img_path = 'macd.jpg'
    ocr = CnOcr() 
    result = ocr.ocr(img_path)
    print(result)

# 具体参数设置参考：https://cnocr.readthedocs.io/zh/latest/usage/
