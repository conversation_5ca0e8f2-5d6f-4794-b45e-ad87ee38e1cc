# MySQL数据库查询功能使用说明

## 功能概述

`request_sql` 函数提供了从MySQL数据库查询股票数据的功能，支持：
- 单个股票代码查询（传入字符串）
- 多个股票代码批量查询（传入列表）
- 指定数据周期查询
- 返回pandas DataFrame格式的数据

## 安装依赖

项目已经在 `pyproject.toml` 中添加了 `pymysql` 依赖，运行以下命令安装：

```bash
pip install pymysql
```

或者如果使用uv：

```bash
uv add pymysql
```

## 数据库设置

### 1. 创建数据库和表

使用提供的 `database_schema.sql` 文件创建数据库表结构：

```bash
mysql -u root -p < database_schema.sql
```

### 2. 配置数据库连接

在使用前需要配置数据库连接信息：

```python
from src.xtquant_data_class import xtdata_class

# 创建实例
xda = xtdata_class()

# 配置数据库连接
xda.set_database_config(
    host='localhost',        # 数据库主机
    port=3306,              # 数据库端口
    user='root',            # 用户名
    password='your_password', # 密码
    database='stock_data'    # 数据库名
)
```

## 使用方法

### 查询单个股票

```python
# 查询单个股票的日线数据
data = xda.request_sql("000001.SZ", "1d")
if data is not None:
    print(data.head())
```

### 查询多个股票

```python
# 查询多个股票的日线数据
stock_list = ["000001.SZ", "000002.SZ", "600000.SH"]
data = xda.request_sql(stock_list, "1d")
if data is not None:
    print(data.head())
```

### 查询不同周期数据

```python
# 查询小时线数据
hourly_data = xda.request_sql("000001.SZ", "1h")

# 查询5分钟线数据
minute_data = xda.request_sql("000001.SZ", "5m")
```

## 返回数据格式

函数返回pandas DataFrame，包含以下列：
- `stock_code`: 股票代码
- `timestamp`: 时间戳（作为索引）
- `open`: 开盘价
- `high`: 最高价
- `low`: 最低价
- `close`: 收盘价
- `volume`: 成交量
- `amount`: 成交额

## 数据库表结构

### stock_data 表

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | BIGINT | 主键ID |
| stock_code | VARCHAR(20) | 股票代码 |
| period | VARCHAR(10) | 数据周期 |
| timestamp | DATETIME | 时间戳 |
| open | DECIMAL(10,3) | 开盘价 |
| high | DECIMAL(10,3) | 最高价 |
| low | DECIMAL(10,3) | 最低价 |
| close | DECIMAL(10,3) | 收盘价 |
| volume | BIGINT | 成交量 |
| amount | DECIMAL(20,2) | 成交额 |

## 错误处理

函数包含完整的错误处理机制：
- 数据库连接失败时返回 None
- 查询无结果时返回 None
- 参数类型错误时返回 None
- 所有错误信息都会记录到日志中

## 注意事项

1. **数据库权限**：确保数据库用户有足够的权限访问目标数据库和表
2. **网络连接**：确保能够连接到MySQL服务器
3. **数据格式**：股票代码格式应为 "XXXXXX.XX"（如 "000001.SZ"）
4. **内存使用**：查询大量数据时注意内存使用情况
5. **连接管理**：函数会自动管理数据库连接的打开和关闭

## 完整示例

```python
from src.xtquant_data_class import xtdata_class
import logging

# 设置日志级别
logging.basicConfig(level=logging.INFO)

# 创建实例
xda = xtdata_class()

# 配置数据库
xda.set_database_config(
    host='localhost',
    user='root',
    password='your_password',
    database='stock_data'
)

# 查询数据
try:
    # 单个股票查询
    single_stock = xda.request_sql("000001.SZ", "1d")
    if single_stock is not None:
        print("单个股票数据:")
        print(single_stock.head())
    
    # 多个股票查询
    multi_stocks = xda.request_sql(["000001.SZ", "000002.SZ"], "1d")
    if multi_stocks is not None:
        print("\n多个股票数据:")
        print(multi_stocks.head())
        
except Exception as e:
    print(f"查询失败: {e}")
```
